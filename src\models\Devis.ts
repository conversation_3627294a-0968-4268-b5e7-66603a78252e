import { Timestamp } from 'firebase/firestore';
import { 
  BaseEntity, 
  ValidationResult,
  ValidationError,
  CreateEntity,
  UpdateEntity,
  sanitizeString,
  VALIDATION_LIMITS
} from './common';

// ============================================================================
// DEVIS INTERFACES
// ============================================================================

/**
 * Devis status enum - simplified to match database schema
 */
export enum DevisStatus {
  BROUILLON = 'brouillon',
  ENVOYE = 'envoye',
  ACCEPTE = 'accepte',
  REFUSE = 'refuse',
  EXPIRE = 'expire'
}

/**
 * Devis priority enumeration
 */
export enum DevisPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

/**
 * Devis line item interface - simplified to match database schema
 */
export interface DevisLineItem {
  designation: string; // Désignation du produit/service
  prixUnitaireHT: number; // Prix unitaire HT
  produitId: string; // ID du produit
  quantite: number; // Quantité
  reference: string; // Référence du produit
  unite: string; // Unité (pièce, kg, etc.)
}

/**
 * Main Devis interface - simplified to match database schema
 */
export interface Devis extends BaseEntity {
  // Basic Information
  clientId: string; // ID du client
  numero: string; // Numéro du devis
  
  // Content
  items: DevisLineItem[]; // Articles du devis
  
  // Pricing
  remiseMontant: number; // Montant de remise
  remisePourcentage: number; // Pourcentage de remise
  tauxTva: number; // Taux de TVA
  
  // Dates
  dateCreation: string; // Date de création (format ISO string)
  dateExpiration: string; // Date d'expiration (format ISO string)
  
  // Status and Notes
  statut: DevisStatus; // Statut du devis
  notes: string; // Notes
  conditionsValidite: string; // Conditions de validité
}

// ============================================================================
// DEVIS FORM INTERFACES
// ============================================================================

/**
 * Devis form data interface (for forms)
 */
export interface DevisFormData {
  clientId: string;
  numero: string;
  items: DevisLineItem[];
  remiseMontant: number;
  remisePourcentage: number;
  tauxTva: number;
  dateCreation: string;
  dateExpiration: string;
  statut: DevisStatus;
  notes: string;
  conditionsValidite: string;
}

/**
 * Devis search/filter interface
 */
export interface DevisSearchFilters {
  clientId?: string;
  numero?: string;
  statut?: DevisStatus;
  dateCreationAfter?: string;
  dateCreationBefore?: string;
  dateExpirationAfter?: string;
  dateExpirationBefore?: string;
  remiseMontantMin?: number;
  remiseMontantMax?: number;
}

// ============================================================================
// TYPE ALIASES
// ============================================================================

export type CreateDevisData = CreateEntity<Devis>;
export type UpdateDevisData = UpdateEntity<Devis>;
export type DevisWithId = Devis & Required<Pick<Devis, 'id'>>;

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate devis form data
 */
export const validateDevisData = (data: Partial<DevisFormData>): ValidationResult => {
  const errors: ValidationError[] = [];

  // Required fields validation
  if (!data.clientId || data.clientId.trim().length === 0) {
    errors.push({
      field: 'clientId',
      message: 'L\'ID du client est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.numero || data.numero.trim().length === 0) {
    errors.push({
      field: 'numero',
      message: 'Le numéro du devis est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.dateCreation || data.dateCreation.trim().length === 0) {
    errors.push({
      field: 'dateCreation',
      message: 'La date de création est requise',
      code: 'REQUIRED'
    });
  }

  if (!data.dateExpiration || data.dateExpiration.trim().length === 0) {
    errors.push({
      field: 'dateExpiration',
      message: 'La date d\'expiration est requise',
      code: 'REQUIRED'
    });
  }

  if (!data.items || data.items.length === 0) {
    errors.push({
      field: 'items',
      message: 'Au moins un article est requis',
      code: 'REQUIRED'
    });
  }

  // Validate numeric fields
  if (data.remiseMontant !== undefined && data.remiseMontant < 0) {
    errors.push({
      field: 'remiseMontant',
      message: 'Le montant de remise ne peut pas être négatif',
      code: 'INVALID_VALUE'
    });
  }

  if (data.remisePourcentage !== undefined && (data.remisePourcentage < 0 || data.remisePourcentage > 100)) {
    errors.push({
      field: 'remisePourcentage',
      message: 'Le pourcentage de remise doit être entre 0 et 100',
      code: 'INVALID_VALUE'
    });
  }

  if (data.tauxTva !== undefined && data.tauxTva < 0) {
    errors.push({
      field: 'tauxTva',
      message: 'Le taux de TVA ne peut pas être négatif',
      code: 'INVALID_VALUE'
    });
  }

  // Validate items
  if (data.items) {
    data.items.forEach((item, index) => {
      if (!item.designation || item.designation.trim().length === 0) {
        errors.push({
          field: `items[${index}].designation`,
          message: 'La désignation est requise',
          code: 'REQUIRED'
        });
      }

      if (!item.reference || item.reference.trim().length === 0) {
        errors.push({
          field: `items[${index}].reference`,
          message: 'La référence est requise',
          code: 'REQUIRED'
        });
      }

      if (item.quantite <= 0) {
        errors.push({
          field: `items[${index}].quantite`,
          message: 'La quantité doit être positive',
          code: 'INVALID_VALUE'
        });
      }

      if (item.prixUnitaireHT < 0) {
        errors.push({
          field: `items[${index}].prixUnitaireHT`,
          message: 'Le prix unitaire HT ne peut pas être négatif',
          code: 'INVALID_VALUE'
        });
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Sanitize devis data before saving
 */
export const sanitizeDevisData = (data: DevisFormData): DevisFormData => {
  return {
    ...data,
    clientId: sanitizeString(data.clientId),
    numero: sanitizeString(data.numero),
    notes: sanitizeString(data.notes),
    conditionsValidite: sanitizeString(data.conditionsValidite),
    dateCreation: data.dateCreation.trim(),
    dateExpiration: data.dateExpiration.trim(),
    items: data.items.map(item => ({
      ...item,
      designation: sanitizeString(item.designation),
      reference: sanitizeString(item.reference),
      produitId: sanitizeString(item.produitId),
      unite: sanitizeString(item.unite)
    }))
  };
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Calculate devis total HT from items
 */
export const calculateDevisTotalHT = (items: DevisLineItem[]): number => {
  return items.reduce((total, item) => total + (item.prixUnitaireHT * item.quantite), 0);
};

/**
 * Calculate devis total TTC
 */
export const calculateDevisTotalTTC = (devis: Devis): number => {
  const totalHT = calculateDevisTotalHT(devis.items);
  const remise = devis.remisePourcentage > 0 
    ? (totalHT * devis.remisePourcentage) / 100 
    : devis.remiseMontant;
  const totalHTApresRemise = totalHT - remise;
  const tva = (totalHTApresRemise * devis.tauxTva) / 100;
  return totalHTApresRemise + tva;
};

/**
 * Get devis status display text
 */
export const getDevisStatusText = (status: DevisStatus): string => {
  const statusMap = {
    [DevisStatus.BROUILLON]: 'Brouillon',
    [DevisStatus.ENVOYE]: 'Envoyé',
    [DevisStatus.ACCEPTE]: 'Accepté',
    [DevisStatus.REFUSE]: 'Refusé',
    [DevisStatus.EXPIRE]: 'Expiré'
  };
  
  return statusMap[status] || status;
};

/**
 * Check if devis is expired
 */
export const isDevisExpired = (devis: Devis): boolean => {
  return new Date(devis.dateExpiration) < new Date();
};

/**
 * Check if devis can be modified
 */
export const canModifyDevis = (devis: Devis): boolean => {
  return devis.statut === DevisStatus.BROUILLON;
};

/**
 * Generate devis number
 */
export const generateDevisNumber = (prefix: string = 'DEV'): string => {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}-${timestamp}-${random}`;
};

/**
 * Format devis date
 */
export const formatDevisDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('fr-FR');
};
