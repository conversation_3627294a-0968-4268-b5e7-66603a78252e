import React, { createContext, useState, useEffect, ReactNode } from "react";
import { FirebaseService } from "../firebase/services";
import {
  Client,
  Commercial,
  Product,
  Order,
  Merchandizer,
  Catalogue,
  Mission,
  Devis,
} from "../models";

interface AppContextType {
  // Data
  clients: Client[];
  commercials: Commercial[];
  products: Product[];
  orders: Order[];
  merchandizers: Merchandizer[];
  catalogues: Catalogue[];
  missions: Mission[];
  devis: Devis[];

  // Loading states
  loading: boolean;
  clientsLoading: boolean;
  commercialsLoading: boolean;
  productsLoading: boolean;
  ordersLoading: boolean;
  merchandizersLoading: boolean;
  cataloguesLoading: boolean;
  missionsLoading: boolean;
  devisLoading: boolean;

  // CRUD operations
  addClient: (
    client: Omit<Client, "id" | "createdAt" | "updatedAt">
  ) => Promise<string>;
  updateClient: (id: string, client: Partial<Client>) => Promise<void>;
  deleteClient: (id: string) => Promise<void>;

  addCommercial: (
    commercial: Omit<Commercial, "id" | "createdAt" | "updatedAt">
  ) => Promise<string>;
  updateCommercial: (
    id: string,
    commercial: Partial<Commercial>
  ) => Promise<void>;
  deleteCommercial: (id: string) => Promise<void>;

  addProduct: (
    product: Omit<Product, "id" | "createdAt" | "updatedAt">
  ) => Promise<string>;
  updateProduct: (id: string, product: Partial<Product>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;

  addOrder: (
    order: Omit<Order, "id" | "createdAt" | "updatedAt">
  ) => Promise<string>;
  updateOrder: (id: string, order: Partial<Order>) => Promise<void>;
  deleteOrder: (id: string) => Promise<void>;

  addMerchandizer: (
    merchandizer: Omit<Merchandizer, "id" | "createdAt" | "updatedAt">
  ) => Promise<string>;
  updateMerchandizer: (
    id: string,
    merchandizer: Partial<Merchandizer>
  ) => Promise<void>;
  deleteMerchandizer: (id: string) => Promise<void>;

  addCatalogue: (
    catalogue: Omit<Catalogue, "id" | "createdAt" | "updatedAt">
  ) => Promise<string>;
  updateCatalogue: (id: string, catalogue: Partial<Catalogue>) => Promise<void>;
  deleteCatalogue: (id: string) => Promise<void>;

  addMission: (
    mission: Omit<Mission, "id" | "createdAt" | "updatedAt">
  ) => Promise<string>;
  updateMission: (id: string, mission: Partial<Mission>) => Promise<void>;
  deleteMission: (id: string) => Promise<void>;

  addDevis: (
    devis: Omit<Devis, "id" | "createdAt" | "updatedAt">
  ) => Promise<string>;
  updateDevis: (id: string, devis: Partial<Devis>) => Promise<void>;
  deleteDevis: (id: string) => Promise<void>;

  // Refresh data
  refreshAll: () => Promise<void>;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export { AppContext };

interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [clients, setClients] = useState<Client[]>([]);
  const [commercials, setCommercials] = useState<Commercial[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [merchandizers, setMerchandizers] = useState<Merchandizer[]>([]);
  const [catalogues, setCatalogues] = useState<Catalogue[]>([]);
  const [missions, setMissions] = useState<Mission[]>([]);
  const [devis, setDevis] = useState<Devis[]>([]);

  const [loading, setLoading] = useState(true);
  const [clientsLoading, setClientsLoading] = useState(false);
  const [commercialsLoading, setCommercialsLoading] = useState(false);
  const [productsLoading, setProductsLoading] = useState(false);
  const [ordersLoading, setOrdersLoading] = useState(false);
  const [merchandizersLoading, setMerchandizersLoading] = useState(false);
  const [cataloguesLoading, setCataloguesLoading] = useState(false);
  const [missionsLoading, setMissionsLoading] = useState(false);
  const [devisLoading, setDevisLoading] = useState(false);

  // Load all data on component mount
  useEffect(() => {
    refreshAll();
  }, []);

  const refreshAll = async () => {
    setLoading(true);
    try {
      const [
        clientsData,
        commercialsData,
        productsData,
        ordersData,
        merchandizersData,
        cataloguesData,
        missionsData,
        devisData,
      ] = await Promise.all([
        FirebaseService.getClients(),
        FirebaseService.getCommercials(),
        FirebaseService.getProducts(),
        FirebaseService.getOrders(),
        FirebaseService.getMerchandizers(),
        FirebaseService.getCatalogues(),
        FirebaseService.getMissions(),
        FirebaseService.getDevis(),
      ]);

      setClients(clientsData);
      setCommercials(commercialsData);
      setProducts(productsData);
      setOrders(ordersData);
      setMerchandizers(merchandizersData);
      setMissions(missionsData);
      setCatalogues(cataloguesData);
      setDevis(devisData);
    } catch (error) {
      console.error("Error loading data:", error);
    } finally {
      setLoading(false);
    }
  };

  // Client operations
  const addClient = async (
    client: Omit<Client, "id" | "createdAt" | "updatedAt">
  ) => {
    setClientsLoading(true);
    try {
      const id = await FirebaseService.addClient(client);
      const updatedClients = await FirebaseService.getClients();
      setClients(updatedClients);
      return id;
    } catch (error) {
      console.error("Error adding client:", error);
      throw error;
    } finally {
      setClientsLoading(false);
    }
  };

  const updateClient = async (id: string, client: Partial<Client>) => {
    setClientsLoading(true);
    try {
      await FirebaseService.updateClient(id, client);
      const updatedClients = await FirebaseService.getClients();
      setClients(updatedClients);
    } catch (error) {
      console.error("Error updating client:", error);
      throw error;
    } finally {
      setClientsLoading(false);
    }
  };

  const deleteClient = async (id: string) => {
    setClientsLoading(true);
    try {
      await FirebaseService.deleteClient(id);
      const updatedClients = await FirebaseService.getClients();
      setClients(updatedClients);
    } catch (error) {
      console.error("Error deleting client:", error);
      throw error;
    } finally {
      setClientsLoading(false);
    }
  };

  // Commercial operations
  const addCommercial = async (
    commercial: Omit<Commercial, "id" | "createdAt" | "updatedAt">
  ) => {
    setCommercialsLoading(true);
    try {
      const id = await FirebaseService.addCommercial(commercial);
      const updatedCommercials = await FirebaseService.getCommercials();
      setCommercials(updatedCommercials);
      return id;
    } catch (error) {
      console.error("Error adding commercial:", error);
      throw error;
    } finally {
      setCommercialsLoading(false);
    }
  };

  const updateCommercial = async (
    id: string,
    commercial: Partial<Commercial>
  ) => {
    setCommercialsLoading(true);
    try {
      await FirebaseService.updateCommercial(id, commercial);
      const updatedCommercials = await FirebaseService.getCommercials();
      setCommercials(updatedCommercials);
    } catch (error) {
      console.error("Error updating commercial:", error);
      throw error;
    } finally {
      setCommercialsLoading(false);
    }
  };

  const deleteCommercial = async (id: string) => {
    setCommercialsLoading(true);
    try {
      await FirebaseService.deleteCommercial(id);
      const updatedCommercials = await FirebaseService.getCommercials();
      setCommercials(updatedCommercials);
    } catch (error) {
      console.error("Error deleting commercial:", error);
      throw error;
    } finally {
      setCommercialsLoading(false);
    }
  };

  // Product operations
  const addProduct = async (
    product: Omit<Product, "id" | "createdAt" | "updatedAt">
  ) => {
    setProductsLoading(true);
    try {
      const id = await FirebaseService.addProduct(product);
      const updatedProducts = await FirebaseService.getProducts();
      setProducts(updatedProducts);
      return id;
    } catch (error) {
      console.error("Error adding product:", error);
      throw error;
    } finally {
      setProductsLoading(false);
    }
  };

  const updateProduct = async (id: string, product: Partial<Product>) => {
    setProductsLoading(true);
    try {
      await FirebaseService.updateProduct(id, product);
      const updatedProducts = await FirebaseService.getProducts();
      setProducts(updatedProducts);
    } catch (error) {
      console.error("Error updating product:", error);
      throw error;
    } finally {
      setProductsLoading(false);
    }
  };

  const deleteProduct = async (id: string) => {
    setProductsLoading(true);
    try {
      await FirebaseService.deleteProduct(id);
      const updatedProducts = await FirebaseService.getProducts();
      setProducts(updatedProducts);
    } catch (error) {
      console.error("Error deleting product:", error);
      throw error;
    } finally {
      setProductsLoading(false);
    }
  };

  // Order operations
  const addOrder = async (
    order: Omit<Order, "id" | "createdAt" | "updatedAt">
  ) => {
    setOrdersLoading(true);
    try {
      const id = await FirebaseService.addOrder(order);
      const updatedOrders = await FirebaseService.getOrders();
      setOrders(updatedOrders);
      return id;
    } catch (error) {
      console.error("Error adding order:", error);
      throw error;
    } finally {
      setOrdersLoading(false);
    }
  };

  const updateOrder = async (id: string, order: Partial<Order>) => {
    setOrdersLoading(true);
    try {
      await FirebaseService.updateOrder(id, order);
      const updatedOrders = await FirebaseService.getOrders();
      setOrders(updatedOrders);
    } catch (error) {
      console.error("Error updating order:", error);
      throw error;
    } finally {
      setOrdersLoading(false);
    }
  };

  const deleteOrder = async (id: string) => {
    setOrdersLoading(true);
    try {
      await FirebaseService.deleteOrder(id);
      const updatedOrders = await FirebaseService.getOrders();
      setOrders(updatedOrders);
    } catch (error) {
      console.error("Error deleting order:", error);
      throw error;
    } finally {
      setOrdersLoading(false);
    }
  };

  // Merchandizer CRUD operations
  const addMerchandizer = async (
    merchandizer: Omit<Merchandizer, "id" | "createdAt" | "updatedAt">
  ) => {
    setMerchandizersLoading(true);
    try {
      const id = await FirebaseService.addMerchandizer(merchandizer);
      const updatedMerchandizers = await FirebaseService.getMerchandizers();
      setMerchandizers(updatedMerchandizers);
      return id;
    } catch (error) {
      console.error("Error adding merchandizer:", error);
      throw error;
    } finally {
      setMerchandizersLoading(false);
    }
  };

  const updateMerchandizer = async (
    id: string,
    merchandizer: Partial<Merchandizer>
  ) => {
    setMerchandizersLoading(true);
    try {
      await FirebaseService.updateMerchandizer(id, merchandizer);
      const updatedMerchandizers = await FirebaseService.getMerchandizers();
      setMerchandizers(updatedMerchandizers);
    } catch (error) {
      console.error("Error updating merchandizer:", error);
      throw error;
    } finally {
      setMerchandizersLoading(false);
    }
  };

  const deleteMerchandizer = async (id: string) => {
    setMerchandizersLoading(true);
    try {
      await FirebaseService.deleteMerchandizer(id);
      const updatedMerchandizers = await FirebaseService.getMerchandizers();
      setMerchandizers(updatedMerchandizers);
    } catch (error) {
      console.error("Error deleting merchandizer:", error);
      throw error;
    } finally {
      setMerchandizersLoading(false);
    }
  };

  // Catalogue CRUD operations
  const addCatalogue = async (
    catalogue: Omit<Catalogue, "id" | "createdAt" | "updatedAt">
  ) => {
    setCataloguesLoading(true);
    try {
      const id = await FirebaseService.addCatalogue(catalogue);
      const updatedCatalogues = await FirebaseService.getCatalogues();
      setCatalogues(updatedCatalogues);
      return id;
    } catch (error) {
      console.error("Error adding catalogue:", error);
      throw error;
    } finally {
      setCataloguesLoading(false);
    }
  };

  const updateCatalogue = async (id: string, catalogue: Partial<Catalogue>) => {
    setCataloguesLoading(true);
    try {
      await FirebaseService.updateCatalogue(id, catalogue);
      const updatedCatalogues = await FirebaseService.getCatalogues();
      setCatalogues(updatedCatalogues);
    } catch (error) {
      console.error("Error updating catalogue:", error);
      throw error;
    } finally {
      setCataloguesLoading(false);
    }
  };

  const deleteCatalogue = async (id: string) => {
    setCataloguesLoading(true);
    try {
      await FirebaseService.deleteCatalogue(id);
      const updatedCatalogues = await FirebaseService.getCatalogues();
      setCatalogues(updatedCatalogues);
    } catch (error) {
      console.error("Error deleting catalogue:", error);
      throw error;
    } finally {
      setCataloguesLoading(false);
    }
  };

  // Mission CRUD operations
  const addMission = async (
    mission: Omit<Mission, "id" | "createdAt" | "updatedAt">
  ) => {
    setMissionsLoading(true);
    try {
      const id = await FirebaseService.addMission(mission);
      const updatedMissions = await FirebaseService.getMissions();
      setMissions(updatedMissions);
      return id;
    } catch (error) {
      console.error("Error adding mission:", error);
      throw error;
    } finally {
      setMissionsLoading(false);
    }
  };

  const updateMission = async (id: string, mission: Partial<Mission>) => {
    setMissionsLoading(true);
    try {
      await FirebaseService.updateMission(id, mission);
      const updatedMissions = await FirebaseService.getMissions();
      setMissions(updatedMissions);
    } catch (error) {
      console.error("Error updating mission:", error);
      throw error;
    } finally {
      setMissionsLoading(false);
    }
  };

  const deleteMission = async (id: string) => {
    setMissionsLoading(true);
    try {
      await FirebaseService.deleteMission(id);
      const updatedMissions = await FirebaseService.getMissions();
      setMissions(updatedMissions);
    } catch (error) {
      console.error("Error deleting mission:", error);
      throw error;
    } finally {
      setMissionsLoading(false);
    }
  };

  // Devis CRUD operations
  const addDevis = async (
    devis: Omit<Devis, "id" | "createdAt" | "updatedAt">
  ) => {
    setDevisLoading(true);
    try {
      const id = await FirebaseService.addDevis(devis);
      const updatedDevis = await FirebaseService.getDevis();
      setDevis(updatedDevis);
      return id;
    } catch (error) {
      console.error("Error adding devis:", error);
      throw error;
    } finally {
      setDevisLoading(false);
    }
  };

  const updateDevis = async (id: string, devis: Partial<Devis>) => {
    setDevisLoading(true);
    try {
      await FirebaseService.updateDevis(id, devis);
      const updatedDevis = await FirebaseService.getDevis();
      setDevis(updatedDevis);
    } catch (error) {
      console.error("Error updating devis:", error);
      throw error;
    } finally {
      setDevisLoading(false);
    }
  };

  const deleteDevis = async (id: string) => {
    setDevisLoading(true);
    try {
      await FirebaseService.deleteDevis(id);
      const updatedDevis = await FirebaseService.getDevis();
      setDevis(updatedDevis);
    } catch (error) {
      console.error("Error deleting devis:", error);
      throw error;
    } finally {
      setDevisLoading(false);
    }
  };

  const value: AppContextType = {
    clients,
    commercials,
    products,
    orders,
    merchandizers,
    catalogues,
    missions,
    devis,
    loading,
    clientsLoading,
    commercialsLoading,
    productsLoading,
    ordersLoading,
    merchandizersLoading,
    cataloguesLoading,
    missionsLoading,
    devisLoading,
    addClient,
    updateClient,
    deleteClient,
    addCommercial,
    updateCommercial,
    deleteCommercial,
    addProduct,
    updateProduct,
    deleteProduct,
    addOrder,
    updateOrder,
    deleteOrder,
    addMerchandizer,
    updateMerchandizer,
    deleteMerchandizer,
    addCatalogue,
    updateCatalogue,
    deleteCatalogue,
    addMission,
    updateMission,
    deleteMission,
    addDevis,
    updateDevis,
    deleteDevis,
    refreshAll,
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

export default AppProvider;
