import React, { useState, useEffect } from "react";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Mail,
  Phone,
  Eye,
  Filter,
  TrendingUp,
  Users,
} from "lucide-react";
import { useApp } from "../hooks/useApp";
import { CommercialModal } from "../components/modals";
import { Commercial } from "../firebase/services";
import ConfirmationModal from "../components/ui/ConfirmationModal";

const CommercialManagement: React.FC = () => {
  const { commercials, loading, deleteCommercial } = useApp();
  const [searchTerm, setSearchTerm] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [editingCommercial, setEditingCommercial] = useState<Commercial | null>(
    null
  );
  const [modalMode, setModalMode] = useState<"create" | "edit" | "view">(
    "create"
  );
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    commercial: Commercial | null;
    loading: boolean;
  }>({ isOpen: false, commercial: null, loading: false });

  const filteredCommercials = commercials.filter(
    (commercial) =>
      commercial.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      commercial.nomComplet.toLowerCase().includes(searchTerm.toLowerCase()) ||
      commercial.territoire.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleEdit = (commercial: Commercial) => {
    setEditingCommercial(commercial);
    setModalMode("edit");
    setShowModal(true);
  };

  const handleView = (commercial: Commercial) => {
    setEditingCommercial(commercial);
    setModalMode("view");
    setShowModal(true);
  };

  const handleDelete = (commercial: Commercial) => {
    setDeleteConfirmation({ isOpen: true, commercial, loading: false });
  };

  const handleConfirmDelete = async () => {
    if (!deleteConfirmation.commercial?.id) return;

    setDeleteConfirmation((prev) => ({ ...prev, loading: true }));

    try {
      await deleteCommercial(deleteConfirmation.commercial.id);
      setDeleteConfirmation({
        isOpen: false,
        commercial: null,
        loading: false,
      });
    } catch (error) {
      console.error("Error deleting commercial:", error);
      setDeleteConfirmation((prev) => ({ ...prev, loading: false }));
    }
  };

  const handleCancelDelete = () => {
    setDeleteConfirmation({ isOpen: false, commercial: null, loading: false });
  };

  const handleAdd = () => {
    setEditingCommercial(null);
    setModalMode("create");
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setEditingCommercial(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">
          Gestion des Commerciaux
        </h1>
        <button
          onClick={handleAdd}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Ajouter un Commercial</span>
        </button>
      </div>

      {/* Search Bar */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Rechercher des commerciaux..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Commercials Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Commercial
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Territoire
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Mobile
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Département
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredCommercials.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-12 text-center">
                    <div className="flex flex-col items-center">
                      <Users className="h-12 w-12 text-gray-400 mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Pas de commerciaux
                      </h3>
                      <p className="text-gray-500 mb-4">
                        {searchTerm
                          ? "Aucun commercial ne correspond à vos critères de recherche."
                          : "Commencez par ajouter votre premier commercial."}
                      </p>
                      {!searchTerm && (
                        <button
                          onClick={handleAdd}
                          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                        >
                          <Plus className="h-4 w-4" />
                          <span>Ajouter un Commercial</span>
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ) : (
                filteredCommercials.map((commercial) => (
                  <tr
                    key={commercial.id}
                    className="hover:bg-gray-50 transition-colors"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {commercial.nomComplet}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center space-x-4">
                          <span className="flex items-center">
                            <Mail className="h-3 w-3 mr-1" />
                            {commercial.email}
                          </span>
                          <span className="flex items-center">
                            <Phone className="h-3 w-3 mr-1" />
                            {commercial.telephone}
                          </span>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {commercial.territoire}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {commercial.mobile || "N/A"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {commercial.department || "N/A"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          commercial.status === "active"
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {commercial.status === "active" ? "Actif" : "Inactif"}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleView(commercial)}
                          className="text-gray-600 hover:text-gray-900 transition-colors"
                          title="Voir"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleEdit(commercial)}
                          className="text-blue-600 hover:text-blue-900 transition-colors"
                          title="Modifier"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(commercial)}
                          className="text-red-600 hover:text-red-900 transition-colors"
                          title="Supprimer"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      <CommercialModal
        isOpen={showModal}
        onClose={handleCloseModal}
        commercial={editingCommercial}
        mode={modalMode}
      />

      <ConfirmationModal
        isOpen={deleteConfirmation.isOpen}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title="Supprimer le commercial"
        message={`Êtes-vous sûr de vouloir supprimer le commercial "${deleteConfirmation.commercial?.name}" ? Cette action est irréversible.`}
        confirmText="Supprimer"
        cancelText="Annuler"
        type="danger"
        loading={deleteConfirmation.loading}
      />

      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span>Chargement...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CommercialManagement;
