import { Timestamp } from 'firebase/firestore';
import {
  BaseEntity,
  EntityStatus,
  ValidationResult,
  ValidationError,
  CreateEntity,
  UpdateEntity,
  sanitizeString,
  VALIDATION_LIMITS
} from './common';

// ============================================================================
// CATALOGUE INTERFACES
// ============================================================================

/**
 * Catalogue type enum - simplified to match database schema
 */
export enum CatalogueType {
  PRODUITS = 'produits',
  SERVICES = 'services',
  PROMOTIONS = 'promotions'
}

/**
 * Catalogue format enumeration
 */
export enum CatalogueFormat {
  PDF = 'pdf',
  HTML = 'html',
  INTERACTIVE = 'interactive'
}

/**
 * Distribution channel enumeration
 */
export enum DistributionChannel {
  EMAIL = 'email',
  WEB = 'web',
  MOBILE = 'mobile',
  PRINT = 'print'
}

/**
 * Main Catalogue interface - simplified to match database schema
 */
export interface Catalogue extends BaseEntity {
  // Basic Information
  nom: string; // Nom du catalogue
  description: string; // Description du catalogue
  type: CatalogueType; // Type de catalogue

  // File Information
  urlPdf: string; // URL du fichier PDF
  cheminLocal: string | null; // Chemin local du fichier (peut être null)
  imagePreview: string | null; // Image de prévisualisation (peut être null)

  // Status and Metadata
  isActive: boolean; // Statut actif/inactif
  metadata: any | null; // Métadonnées additionnelles (peut être null)

  // Dates
  dateCreation: Timestamp; // Date de création
  dateModification: Timestamp; // Date de modification
}

// ============================================================================
// CATALOGUE FORM INTERFACES
// ============================================================================

/**
 * Catalogue form data interface (for forms)
 */
export interface CatalogueFormData {
  nom: string;
  description: string;
  type: CatalogueType;
  urlPdf: string;
  cheminLocal?: string | null;
  imagePreview?: string | null;
  isActive: boolean;
  metadata?: any | null;
}

/**
 * Catalogue search/filter interface
 */
export interface CatalogueSearchFilters {
  nom?: string;
  description?: string;
  type?: CatalogueType;
  isActive?: boolean;
  dateCreationAfter?: Timestamp;
  dateCreationBefore?: Timestamp;
  dateModificationAfter?: Timestamp;
  dateModificationBefore?: Timestamp;
}

// ============================================================================
// TYPE ALIASES
// ============================================================================

export type CreateCatalogueData = CreateEntity<Catalogue>;
export type UpdateCatalogueData = UpdateEntity<Catalogue>;
export type CatalogueWithId = Catalogue & Required<Pick<Catalogue, 'id'>>;

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate catalogue form data
 */
export const validateCatalogueData = (data: Partial<CatalogueFormData>): ValidationResult => {
  const errors: ValidationError[] = [];

  // Required fields validation
  if (!data.nom || data.nom.trim().length === 0) {
    errors.push({
      field: 'nom',
      message: 'Le nom du catalogue est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.description || data.description.trim().length === 0) {
    errors.push({
      field: 'description',
      message: 'La description est requise',
      code: 'REQUIRED'
    });
  }

  if (!data.urlPdf || data.urlPdf.trim().length === 0) {
    errors.push({
      field: 'urlPdf',
      message: 'L\'URL du PDF est requise',
      code: 'REQUIRED'
    });
  }

  // URL validation
  if (data.urlPdf && !isValidUrl(data.urlPdf)) {
    errors.push({
      field: 'urlPdf',
      message: 'L\'URL du PDF doit être valide',
      code: 'INVALID_FORMAT'
    });
  }

  // Length validations
  if (data.nom && data.nom.length > VALIDATION_LIMITS.NAME_MAX_LENGTH) {
    errors.push({
      field: 'nom',
      message: `Le nom ne peut pas dépasser ${VALIDATION_LIMITS.NAME_MAX_LENGTH} caractères`,
      code: 'INVALID_LENGTH'
    });
  }

  if (data.description && data.description.length > VALIDATION_LIMITS.DESCRIPTION_MAX_LENGTH) {
    errors.push({
      field: 'description',
      message: `La description ne peut pas dépasser ${VALIDATION_LIMITS.DESCRIPTION_MAX_LENGTH} caractères`,
      code: 'INVALID_LENGTH'
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Check if URL is valid
 */
const isValidUrl = (string: string): boolean => {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
};

/**
 * Sanitize catalogue data before saving
 */
export const sanitizeCatalogueData = (data: CatalogueFormData): CatalogueFormData => {
  return {
    ...data,
    nom: sanitizeString(data.nom),
    description: sanitizeString(data.description),
    urlPdf: data.urlPdf.trim(),
    cheminLocal: data.cheminLocal ? sanitizeString(data.cheminLocal) : null,
    imagePreview: data.imagePreview ? sanitizeString(data.imagePreview) : null
  };
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Check if catalogue is active
 */
export const isCatalogueActive = (catalogue: Catalogue): boolean => {
  return catalogue.isActive;
};

/**
 * Get catalogue display name
 */
export const getCatalogueDisplayName = (catalogue: Catalogue): string => {
  return catalogue.nom;
};

/**
 * Get catalogue file extension from URL
 */
export const getCatalogueFileExtension = (catalogue: Catalogue): string => {
  const url = catalogue.urlPdf;
  const extension = url.split('.').pop()?.toLowerCase();
  return extension || '';
};


