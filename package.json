{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@types/react-router-dom": "^5.3.3", "file-saver": "^2.0.5", "firebase": "^11.10.0", "jszip": "^3.10.1", "lucide-react": "^0.344.0", "pdf-parse": "^1.1.1", "pdf2pic": "^3.2.0", "pdfjs-dist": "^5.3.93", "react": "^18.3.1", "react-dom": "^18.3.1", "react-pdf": "^10.0.1", "react-router-dom": "^7.7.0", "tesseract.js": "^6.0.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vite-plugin-svgr": "^4.3.0"}}