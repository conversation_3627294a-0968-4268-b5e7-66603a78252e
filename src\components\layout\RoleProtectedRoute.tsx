import React from "react";
import { useRoleAccess, UserRole } from "../../hooks/useRoleAccess";

interface RoleProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles: UserRole | UserRole[];
  fallback?: React.ReactNode;
}

const RoleProtectedRoute: React.FC<RoleProtectedRouteProps> = ({
  children,
  requiredRoles,
  fallback,
}) => {
  const { hasRole } = useRoleAccess();

  if (!hasRole(requiredRoles)) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="mb-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg
                  className="w-8 h-8 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Accès Refusé
              </h2>
              <p className="text-gray-600">
                Vous n'avez pas les permissions nécessaires pour accéder à cette page.
              </p>
            </div>
            <div className="text-sm text-gray-500">
              Contactez votre administrateur si vous pensez qu'il s'agit d'une erreur.
            </div>
          </div>
        </div>
      )
    );
  }

  return <>{children}</>;
};

export default RoleProtectedRoute;
