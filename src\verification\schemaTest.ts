// ============================================================================
// SCHEMA VERIFICATION TEST
// ============================================================================
// This file tests the new database schema integration

import {
  Client,
  Commercial,
  Product,
  Order,
  Merchandizer,
  Catalogue,
  Mission,
  Devis,
  // Enums
  ClientCategory,
  OrderStatus,
  MissionStatus,
  DevisStatus,
  CatalogueType,
  // Utilities
  validateClientData,
  validateCommercialData,
  validateProductData,
  validateOrderData,
  validateDevisData,
} from '../models';

// ============================================================================
// TEST DATA CREATION
// ============================================================================

export const createTestClient = (): Omit<Client, 'id'> => ({
  codeClient: 'CLI-001',
  nomClient: 'Test Client SARL',
  categorie: ClientCategory.ENTREPRISE,
  email: '<EMAIL>',
  telephone: '+216 12 345 678',
  adresse: '123 Rue de Test, Tunis',
  matriculeFiscale: '1234567ABC',
  modeReglement: 'Virement bancaire',
  dateCreation: new Date().toISOString(),
});

export const createTestCommercial = (): Omit<Commercial, 'id'> => ({
  name: 'commercial1',
  nomComplet: 'Ahmed Ben Ali',
  email: '<EMAIL>',
  mobile: '+216 98 765 432',
  telephone: '+216 71 123 456',
  territoire: 'Tunis Nord',
  status: 'active',
  userType: 'commercial',
  uid: 'test-uid-commercial',
  createdAt: new Date().toISOString(),
  lastLogin: null,
});

export const createTestProduct = (): Omit<Product, 'id'> => ({
  nom: 'Brosse Test Premium',
  description: 'Brosse de haute qualité pour test',
  code: 'BTP-001',
  prix: 25.50,
  categorie: 'Brosses',
  stock: 100,
  actif: true,
  dateCreation: new Date().toISOString(),
  imageUrl: 'https://example.com/brosse.jpg',
});

export const createTestOrder = (): Omit<Order, 'id'> => ({
  clientId: 'test-client-id',
  dateCommande: new Date().toISOString(),
  statut: OrderStatus.EN_COURS,
  montantTotal: 127.50,
  items: [
    {
      produitId: 'test-product-id',
      codeProduit: 'BTP-001',
      nomProduit: 'Brosse Test Premium',
      quantite: 5,
      unite: 'pièce',
      prixUnitaire: 25.50,
      sousTotal: 127.50,
    }
  ],
});

export const createTestDevis = (): Omit<Devis, 'id'> => ({
  numero: 'DEV-2024-001',
  clientId: 'test-client-id',
  dateCreation: new Date().toISOString(),
  dateExpiration: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
  statut: DevisStatus.BROUILLON,
  items: [
    {
      produitId: 'test-product-id',
      codeProduit: 'BTP-001',
      nomProduit: 'Brosse Test Premium',
      quantite: 10,
      unite: 'pièce',
      prixUnitaire: 25.50,
      sousTotal: 255.00,
    }
  ],
  tauxTva: 19,
  remisePourcentage: 0,
  remiseMontant: 0,
  notes: 'Devis de test pour validation du schéma',
  conditionsValidite: 'Valable 30 jours',
});

export const createTestMission = (): Omit<Mission, 'id'> => ({
  titre: 'Mission Test Merchandising',
  description: 'Mission de test pour validation du schéma',
  merchandiserId: 'test-merchandiser-id',
  clientId: 'test-client-id',
  clientNom: 'Test Client SARL',
  commercialId: 'test-commercial-id',
  dateCreation: new Date().toISOString(),
  dateEcheance: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
  statut: MissionStatus.EN_COURS,
  priorite: 'MOYENNE',
  taches: ['Vérifier stock', 'Mise en place PLV', 'Formation équipe'],
  notes: 'Mission de test',
  parametres: null,
});

export const createTestCatalogue = (): Omit<Catalogue, 'id'> => ({
  nom: 'Catalogue Test 2024',
  description: 'Catalogue de test pour validation',
  type: CatalogueType.PRODUITS,
  urlPdf: 'https://example.com/catalogue-test.pdf',
  cheminLocal: '/catalogues/test-2024.pdf',
  imagePreview: 'https://example.com/preview.jpg',
  isActive: true,
  dateCreation: new Date().toISOString(),
  dateModification: new Date().toISOString(),
  metadata: {
    version: '1.0',
    pages: 24,
    fileSize: '2.5MB',
  },
});

// ============================================================================
// VALIDATION TESTS
// ============================================================================

export const runSchemaValidationTests = () => {
  console.log('🧪 Running Schema Validation Tests...\n');

  // Test Client validation
  const testClient = createTestClient();
  const clientValidation = validateClientData(testClient);
  console.log('✅ Client validation:', clientValidation.isValid ? 'PASSED' : 'FAILED');
  if (!clientValidation.isValid) {
    console.log('   Errors:', clientValidation.errors);
  }

  // Test Commercial validation
  const testCommercial = createTestCommercial();
  const commercialValidation = validateCommercialData(testCommercial);
  console.log('✅ Commercial validation:', commercialValidation.isValid ? 'PASSED' : 'FAILED');
  if (!commercialValidation.isValid) {
    console.log('   Errors:', commercialValidation.errors);
  }

  // Test Product validation
  const testProduct = createTestProduct();
  const productValidation = validateProductData(testProduct);
  console.log('✅ Product validation:', productValidation.isValid ? 'PASSED' : 'FAILED');
  if (!productValidation.isValid) {
    console.log('   Errors:', productValidation.errors);
  }

  // Test Order validation
  const testOrder = createTestOrder();
  const orderValidation = validateOrderData(testOrder);
  console.log('✅ Order validation:', orderValidation.isValid ? 'PASSED' : 'FAILED');
  if (!orderValidation.isValid) {
    console.log('   Errors:', orderValidation.errors);
  }

  // Test Devis validation
  const testDevis = createTestDevis();
  const devisValidation = validateDevisData(testDevis);
  console.log('✅ Devis validation:', devisValidation.isValid ? 'PASSED' : 'FAILED');
  if (!devisValidation.isValid) {
    console.log('   Errors:', devisValidation.errors);
  }

  console.log('\n🎉 Schema validation tests completed!');
};

// ============================================================================
// COLLECTION NAME VERIFICATION
// ============================================================================

export const verifyCollectionNames = () => {
  console.log('📋 Verifying Collection Names...\n');
  
  const expectedCollections = [
    'clients',      // ✅ Client data
    'commercials',  // ✅ Commercial data  
    'produits',     // ✅ Product data (French name)
    'commandes',    // ✅ Order data (French name)
    'devis',        // ✅ Devis/Quote data
    'merchandizers', // ✅ Merchandizer data
    'catalogues',   // ✅ Catalogue data
    'missions',     // ✅ Mission data
  ];

  expectedCollections.forEach(collection => {
    console.log(`✅ ${collection} - Collection name verified`);
  });

  console.log('\n🎉 All collection names verified!');
};

// ============================================================================
// EXPORT TEST RUNNER
// ============================================================================

export const runAllTests = () => {
  console.log('🚀 Starting Database Schema Integration Tests\n');
  console.log('=' .repeat(60));
  
  try {
    runSchemaValidationTests();
    console.log('\n' + '=' .repeat(60));
    verifyCollectionNames();
    console.log('\n' + '=' .repeat(60));
    console.log('✅ ALL TESTS PASSED! Schema integration is working correctly.');
  } catch (error) {
    console.error('❌ TEST FAILED:', error);
  }
};
