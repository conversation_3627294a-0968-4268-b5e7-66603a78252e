import { Timestamp } from 'firebase/firestore';
import {
  BaseEntity,
  ValidationResult,
  ValidationError,
  CreateEntity,
  UpdateEntity,
  isValidEmail,
  isValidPhone,
  sanitizeString,
  VALIDATION_LIMITS
} from './common';

// ============================================================================
// MERCHANDIZER INTERFACES
// ============================================================================

/**
 * Merchandizer status enum - simplified to match database schema
 */
export enum MerchandizerStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive'
}

/**
 * Merchandizer type enumeration
 */
export enum MerchandizerType {
  FULL_TIME = 'full_time',
  PART_TIME = 'part_time',
  FREELANCE = 'freelance'
}

/**
 * Visit type enumeration
 */
export enum VisitType {
  ROUTINE = 'routine',
  AUDIT = 'audit',
  TRAINING = 'training',
  EMERGENCY = 'emergency'
}

/**
 * Store type enumeration
 */
export enum StoreType {
  SUPERMARKET = 'supermarket',
  PHARMACY = 'pharmacy',
  RETAIL = 'retail',
  WHOLESALE = 'wholesale'
}

/**
 * Mobile permission enumeration
 */
export enum MobilePermission {
  FULL_ACCESS = 'full_access',
  LIMITED_ACCESS = 'limited_access',
  NO_ACCESS = 'no_access'
}

/**
 * Account status enumeration
 */
export enum AccountStatus {
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  PENDING = 'pending',
  DISABLED = 'disabled'
}

/**
 * Main Merchandizer interface - simplified to match database schema
 */
export interface Merchandizer extends BaseEntity {
  // Basic Information
  name: string; // Nom du merchandizer
  nomComplet: string; // Nom complet du merchandizer
  email: string; // Email du merchandizer

  // Contact Information
  mobile: string; // Téléphone mobile
  telephone: string; // Téléphone fixe

  // Territory and Status
  territoire: string; // Territoire assigné
  status: MerchandizerStatus; // Statut du merchandizer

  // Authentication
  uid: string; // UID Firebase Auth
  userType: string; // Type d'utilisateur (merchandiser)

  // Dates
  createdAt: Timestamp; // Date de création
  lastLogin: Timestamp; // Dernière connexion
}

// ============================================================================
// MERCHANDIZER FORM INTERFACES
// ============================================================================

/**
 * Merchandizer form data interface (for forms)
 */
export interface MerchandizerFormData {
  name: string;
  nomComplet: string;
  email: string;
  mobile: string;
  telephone: string;
  territoire: string;
  status: MerchandizerStatus;
  uid: string;
  userType: string;
}

/**
 * Merchandizer search/filter interface
 */
export interface MerchandizerSearchFilters {
  name?: string;
  nomComplet?: string;
  email?: string;
  mobile?: string;
  telephone?: string;
  territoire?: string;
  status?: MerchandizerStatus;
  userType?: string;
  createdAtAfter?: Timestamp;
  createdAtBefore?: Timestamp;
  lastLoginAfter?: Timestamp;
  lastLoginBefore?: Timestamp;
}

// ============================================================================
// TYPE ALIASES
// ============================================================================

export type CreateMerchandizerData = CreateEntity<Merchandizer>;
export type UpdateMerchandizerData = UpdateEntity<Merchandizer>;
export type MerchandizerWithId = Merchandizer & Required<Pick<Merchandizer, 'id'>>;

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate merchandizer form data
 */
export const validateMerchandizerData = (data: Partial<MerchandizerFormData>): ValidationResult => {
  const errors: ValidationError[] = [];

  // Required fields validation
  if (!data.name || data.name.trim().length === 0) {
    errors.push({
      field: 'name',
      message: 'Le nom est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.nomComplet || data.nomComplet.trim().length === 0) {
    errors.push({
      field: 'nomComplet',
      message: 'Le nom complet est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.email || data.email.trim().length === 0) {
    errors.push({
      field: 'email',
      message: 'L\'email est requis',
      code: 'REQUIRED'
    });
  } else if (!isValidEmail(data.email)) {
    errors.push({
      field: 'email',
      message: 'Format d\'email invalide',
      code: 'INVALID_FORMAT'
    });
  }

  if (!data.mobile || data.mobile.trim().length === 0) {
    errors.push({
      field: 'mobile',
      message: 'Le mobile est requis',
      code: 'REQUIRED'
    });
  } else if (!isValidPhone(data.mobile)) {
    errors.push({
      field: 'mobile',
      message: 'Format de mobile invalide',
      code: 'INVALID_FORMAT'
    });
  }

  if (!data.telephone || data.telephone.trim().length === 0) {
    errors.push({
      field: 'telephone',
      message: 'Le téléphone est requis',
      code: 'REQUIRED'
    });
  } else if (!isValidPhone(data.telephone)) {
    errors.push({
      field: 'telephone',
      message: 'Format de téléphone invalide',
      code: 'INVALID_FORMAT'
    });
  }

  if (!data.territoire || data.territoire.trim().length === 0) {
    errors.push({
      field: 'territoire',
      message: 'Le territoire est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.uid || data.uid.trim().length === 0) {
    errors.push({
      field: 'uid',
      message: 'L\'UID est requis',
      code: 'REQUIRED'
    });
  }

  // Length validations
  if (data.name && data.name.length > VALIDATION_LIMITS.NAME_MAX_LENGTH) {
    errors.push({
      field: 'name',
      message: `Le nom ne peut pas dépasser ${VALIDATION_LIMITS.NAME_MAX_LENGTH} caractères`,
      code: 'INVALID_LENGTH'
    });
  }

  if (data.email && data.email.length > VALIDATION_LIMITS.EMAIL_MAX_LENGTH) {
    errors.push({
      field: 'email',
      message: `L'email ne peut pas dépasser ${VALIDATION_LIMITS.EMAIL_MAX_LENGTH} caractères`,
      code: 'INVALID_LENGTH'
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};
/**
 * Sanitize merchandizer data before saving
 */
export const sanitizeMerchandizerData = (data: MerchandizerFormData): MerchandizerFormData => {
  return {
    ...data,
    name: sanitizeString(data.name),
    nomComplet: sanitizeString(data.nomComplet),
    email: data.email.toLowerCase().trim(),
    mobile: data.mobile.replace(/[\s\-\(\)]/g, ''),
    telephone: data.telephone.replace(/[\s\-\(\)]/g, ''),
    territoire: sanitizeString(data.territoire),
    uid: sanitizeString(data.uid),
    userType: sanitizeString(data.userType)
  };
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get merchandizer display name
 */
export const getMerchandizerDisplayName = (merchandizer: Merchandizer): string => {
  return merchandizer.nomComplet || merchandizer.name;
};

/**
 * Check if merchandizer is active
 */
export const isMerchandizerActive = (merchandizer: Merchandizer): boolean => {
  return merchandizer.status === MerchandizerStatus.ACTIVE;
};

/**
 * Format merchandizer territory
 */
export const formatMerchandizerTerritory = (territoire: string): string => {
  return territoire.toUpperCase();
};

/**
 * Get merchandizer contact info
 */
export const getMerchandizerContactInfo = (merchandizer: Merchandizer): string => {
  return `${merchandizer.email} | ${merchandizer.mobile}`;
};










