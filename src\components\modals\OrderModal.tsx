import React, { useState, useEffect } from "react";
import { X, Save, Loader2, Plus, Trash2 } from "lucide-react";
import { Order, OrderLineItem, OrderStatus } from "../../models";
import { useApp } from "../../hooks/useApp";

interface OrderModalProps {
  isOpen: boolean;
  onClose: () => void;
  order?: Order | null;
  mode: "create" | "edit" | "view";
}

const OrderModal: React.FC<OrderModalProps> = ({
  isOpen,
  onClose,
  order,
  mode,
}) => {
  const { addOrder, updateOrder, clients, products } = useApp();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    clientId: "",
    dateCommande: new Date().toISOString(),
    statut: OrderStatus.EN_COURS,
    items: [] as OrderLineItem[],
    montantTotal: 0,
  });

  useEffect(() => {
    if (order && (mode === "edit" || mode === "view")) {
      setFormData({
        clientId: order.clientId || "",
        dateCommande: order.dateCommande || new Date().toISOString(),
        statut: order.statut || "en_cours",
        items: order.items || [],
        montantTotal: order.montantTotal || 0,
      });
    } else {
      setFormData({
        clientId: "",
        dateCommande: new Date().toISOString(),
        statut: "en_cours",
        items: [],
        montantTotal: 0,
      });
    }
    setError(null);
  }, [order, mode, isOpen]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const addProductLine = () => {
    setFormData((prev) => ({
      ...prev,
      items: [
        ...prev.items,
        {
          codeProduit: "",
          nomProduit: "",
          prixUnitaire: 0,
          produitId: "",
          quantite: 1,
          sousTotal: 0,
          unite: "pièce",
        },
      ],
    }));
  };

  const removeProductLine = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }));
    calculateTotal();
  };

  const updateProductLine = (
    index: number,
    field: string,
    value: string | number
  ) => {
    setFormData((prev) => {
      const newItems = [...prev.items];
      if (field === "produitId") {
        const selectedProduct = products.find((p) => p.id === value);
        newItems[index] = {
          ...newItems[index],
          produitId: value as string,
          nomProduit: selectedProduct?.nom || "",
          codeProduit: selectedProduct?.code || "",
          prixUnitaire: selectedProduct?.prix || 0,
          sousTotal: (selectedProduct?.prix || 0) * newItems[index].quantite,
        };
      } else if (field === "quantite") {
        newItems[index] = {
          ...newItems[index],
          quantite: value as number,
          sousTotal: newItems[index].prixUnitaire * (value as number),
        };
      } else {
        newItems[index] = {
          ...newItems[index],
          [field]: value,
        };
      }
      return { ...prev, items: newItems };
    });
    setTimeout(calculateTotal, 0);
  };

  const calculateTotal = () => {
    setFormData((prev) => ({
      ...prev,
      montantTotal: prev.items.reduce((sum, item) => sum + item.sousTotal, 0),
    }));
  };

  const validateForm = () => {
    if (!formData.clientId) {
      setError("Le client est requis");
      return false;
    }
    if (formData.items.length === 0) {
      setError("Au moins un produit est requis");
      return false;
    }
    for (let i = 0; i < formData.items.length; i++) {
      const item = formData.items[i];
      if (!item.produitId) {
        setError(`Produit requis pour la ligne ${i + 1}`);
        return false;
      }
      if (item.quantite <= 0) {
        setError(`Quantité invalide pour la ligne ${i + 1}`);
        return false;
      }
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (mode === "view") return;

    if (!validateForm()) return;

    setLoading(true);
    setError(null);

    try {
      // Build order data with only defined values
      const orderData = {
        clientId: formData.clientId,
        dateCommande: formData.dateCommande,
        statut: formData.statut,
        items: formData.items.filter(
          (item) => item.produitId && item.quantite > 0
        ),
        montantTotal: formData.montantTotal,
      };

      if (mode === "create") {
        await addOrder(orderData);
      } else if (mode === "edit" && order?.id) {
        await updateOrder(order.id, orderData);
      }
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Une erreur est survenue");
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const isReadOnly = mode === "view";
  const title =
    mode === "create"
      ? "Nouvelle Commande"
      : mode === "edit"
      ? "Modifier Commande"
      : "Détails Commande";

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form
          onSubmit={handleSubmit}
          className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]"
        >
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <label
                htmlFor="clientId"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Client *
              </label>
              <select
                id="clientId"
                name="clientId"
                value={formData.clientId}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                required
              >
                <option value="">Sélectionner un client</option>
                {clients.map((client) => (
                  <option key={client.id} value={client.id}>
                    {client.nomClient} - {client.company}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label
                htmlFor="commercialId"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Commercial *
              </label>
              <select
                id="commercialId"
                name="commercialId"
                value={formData.commercialId}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                required
              >
                <option value="">Sélectionner un commercial</option>
                {commercials.map((commercial) => (
                  <option key={commercial.id} value={commercial.id}>
                    {commercial.name} - {commercial.territory}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label
                htmlFor="status"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Statut
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              >
                <option value="pending">En attente</option>
                <option value="confirmed">Confirmée</option>
                <option value="delivered">Livrée</option>
                <option value="cancelled">Annulée</option>
              </select>
            </div>
          </div>

          {/* Products Section */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Produits</h3>
              {!isReadOnly && (
                <button
                  type="button"
                  onClick={addProductLine}
                  className="flex items-center space-x-2 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                >
                  <Plus className="h-4 w-4" />
                  <span>Ajouter un produit</span>
                </button>
              )}
            </div>

            <div className="space-y-3">
              {formData.products.map((product, index) => (
                <div
                  key={index}
                  className="grid grid-cols-1 md:grid-cols-5 gap-3 p-3 border border-gray-200 rounded-md"
                >
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Produit *
                    </label>
                    <select
                      value={product.productId}
                      onChange={(e) =>
                        updateProductLine(index, "productId", e.target.value)
                      }
                      disabled={isReadOnly}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                      required
                    >
                      <option value="">Sélectionner un produit</option>
                      {products.map((p) => (
                        <option key={p.id} value={p.id}>
                          {p.name} - {p.price}€
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Quantité *
                    </label>
                    <input
                      type="number"
                      value={product.quantity}
                      onChange={(e) =>
                        updateProductLine(
                          index,
                          "quantity",
                          parseInt(e.target.value) || 0
                        )
                      }
                      disabled={isReadOnly}
                      min="1"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Prix unitaire (DT)
                    </label>
                    <input
                      type="number"
                      value={product.price}
                      onChange={(e) =>
                        updateProductLine(
                          index,
                          "price",
                          parseFloat(e.target.value) || 0
                        )
                      }
                      disabled={isReadOnly}
                      step="0.01"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                    />
                  </div>

                  <div className="flex items-end">
                    {!isReadOnly && (
                      <button
                        type="button"
                        onClick={() => removeProductLine(index)}
                        className="w-full px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center justify-center"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                    {isReadOnly && (
                      <div className="w-full px-3 py-2 text-center font-medium">
                        {(product.quantity * product.price).toFixed(2)}€
                      </div>
                    )}
                  </div>
                </div>
              ))}

              {formData.products.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  Aucun produit ajouté
                </div>
              )}
            </div>

            <div className="mt-4 p-4 bg-gray-50 rounded-md">
              <div className="flex justify-between items-center">
                <span className="text-lg font-medium text-gray-900">
                  Total:
                </span>
                <span className="text-xl font-bold text-blue-600">
                  {formData.totalAmount.toFixed(2)}€
                </span>
              </div>
            </div>
          </div>

          {!isReadOnly && (
            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
              >
                Annuler
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
              >
                {loading && <Loader2 className="h-4 w-4 animate-spin" />}
                <Save className="h-4 w-4" />
                <span>{mode === "create" ? "Créer" : "Sauvegarder"}</span>
              </button>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};

export default OrderModal;
