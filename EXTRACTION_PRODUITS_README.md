# 🚀 Extraction Automatique de Produits - VitaBrosse

## 📋 Vue d'ensemble

Cette fonctionnalité innovante permet d'extraire automatiquement les informations de produits à partir de catalogues PDF et de les associer avec des images pour créer une base de données structurée.

## ✨ Fonctionnalités

### 🔍 Extraction automatique
- **Reconnaissance OCR** : Utilise Tesseract.js pour lire le texte des PDFs
- **Analyse intelligente** : Détecte automatiquement les noms, descriptions, prix et références
- **PDF natif** : Support des PDF texte via PDF.js
- **Multi-pages** : Traite tous les pages du catalogue

### 🖼️ Association d'images
- **Upload multiple** : Téléversez plusieurs images de produits
- **Correspondance automatique** : Algorithme de similarité pour associer images et produits
- **Nommage intelligent** : Utilisez des noms de fichiers descriptifs pour une meilleure correspondance

### 📊 Export et validation
- **Interface de vérification** : Révisez et corrigez les données extraites
- **Score de confiance** : Chaque extraction a un score de fiabilité
- **Export JSON** : Génère un fichier JSON structuré avec toutes les données
- **Édition manuelle** : Possibilité de modifier ou ajouter des produits

## 🛠️ Technologies utilisées

- **Tesseract.js** : OCR (Optical Character Recognition)
- **PDF.js** : Parsing et rendu des fichiers PDF
- **React** : Interface utilisateur moderne
- **TypeScript** : Typage fort pour une meilleure fiabilité

## 📁 Structure des fichiers

```
src/
├── services/
│   └── pdfExtractor.ts          # Service principal d'extraction
├── components/
│   └── modals/
│       ├── CatalogueModal.tsx   # Modal de gestion des catalogues
│       └── ProductExtractionModal.tsx  # Modal d'extraction
└── examples/
    └── ProductExtractionExample.tsx    # Exemple d'utilisation
```

## 🎯 Guide d'utilisation

### 1. Préparer le catalogue PDF

**Format optimal :**
- PDF avec texte sélectionnable (pas d'image scannée)
- Structure claire avec titres de produits
- Prix formatés (ex: 29,99€, $19.99)
- Références produits (SKU, REF, CODE)

**Exemple de structure optimale :**
```
PRODUIT PREMIUM XL
Description détaillée du produit avec ses principales caractéristiques.
Prix: 49,99€
REF: PRD-001
Disponible en plusieurs coloris.
```

### 2. Préparer les images (optionnel)

**Bonnes pratiques :**
- Nommez vos images selon le nom du produit
- Formats supportés : JPG, PNG, GIF
- Qualité suffisante pour reconnaissance
- Une image par produit principal

**Exemples de nommage :**
```
produit_premium_xl.jpg
chaise_ergonomique.png
table_basse_moderne.jpg
```

### 3. Lancer l'extraction

1. **Upload PDF** : Sélectionnez votre catalogue
2. **Upload Images** : Ajoutez les images des produits (optionnel)
3. **Extraction** : Lancez le processus automatique
4. **Vérification** : Révisez et corrigez les résultats
5. **Export** : Téléchargez le fichier JSON

### 4. Résultats obtenus

Le fichier JSON contient :
```json
{
  "catalogue": "Nom du catalogue",
  "extractedAt": "2025-01-17T10:30:00.000Z",
  "totalProducts": 25,
  "products": [
    {
      "id": "product_1_1",
      "name": "PRODUIT PREMIUM XL",
      "description": "Description détaillée du produit...",
      "characteristics": [
        "Disponible en plusieurs coloris",
        "Garantie 2 ans"
      ],
      "price": "49,99€",
      "sku": "PRD-001",
      "category": "Premium",
      "imageUrl": "data:image/jpeg;base64,...",
      "pageNumber": 1,
      "confidence": 0.92
    }
  ]
}
```

## 🎛️ Configuration et personnalisation

### Paramètres d'extraction

Vous pouvez ajuster la précision d'extraction dans `pdfExtractor.ts` :

```typescript
// Seuils de détection
const TITLE_MIN_LENGTH = 5;
const TITLE_MAX_LENGTH = 100;
const DESCRIPTION_MAX_LENGTH = 200;

// Patterns de reconnaissance
const PRICE_PATTERN = /([\d,]+[.,]\d{2}\s*[€$£])/;
const SKU_PATTERN = /(?:REF|SKU|CODE)[\s:]*([A-Z0-9-]+)/i;
```

## 🚀 Fonctionnalités futures

### Version 1.1
- [ ] Support de plus de formats (Word, Excel)
- [ ] IA améliorée pour la catégorisation
- [ ] Traitement par lots de catalogues
- [ ] API REST pour intégration externe

### Version 1.2
- [ ] Reconnaissance d'images de produits (Computer Vision)
- [ ] Templates de catalogues prédéfinis
- [ ] Validation automatique des prix
- [ ] Intégration avec des APIs de produits

---

**Version :** 1.0.0  
**Dernière mise à jour :** Janvier 2025  
**Développé par :** Équipe VitaBrosse
