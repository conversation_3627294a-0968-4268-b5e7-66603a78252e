import React, { useState } from "react";
import {
  Plus,
  Search,
  Download,
  Trash2,
  Upload,
  FileText,
  Calendar,
  Edit,
  Eye,
} from "lucide-react";
import { CatalogueModal } from "../components/modals";
import { useApp } from "../hooks/useApp";
import { Catalogue, formatCatalogueDate } from "../models";

const CatalogueManagement: React.FC = () => {
  const { catalogues, deleteCatalogue } = useApp();

  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    mode: "create" | "edit" | "view";
    catalogue?: Catalogue | null;
  }>({ isOpen: false, mode: "create" });
  const [searchTerm, setSearchTerm] = useState("");

  const filteredCatalogues = catalogues.filter(
    (catalogue) =>
      catalogue.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
      catalogue.type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleEdit = (catalogue: Catalogue) => {
    setModalState({
      isOpen: true,
      mode: "edit",
      catalogue,
    });
  };

  const handleView = (catalogue: Catalogue) => {
    setModalState({
      isOpen: true,
      mode: "view",
      catalogue,
    });
  };

  const handleDelete = (id: string) => {
    if (window.confirm("Êtes-vous sûr de vouloir supprimer ce catalogue ?")) {
      deleteCatalogue(id);
    }
  };

  const handleAdd = () => {
    setModalState({
      isOpen: true,
      mode: "create",
      catalogue: null,
    });
  };

  const handleDownload = (catalogue: Catalogue) => {
    if (catalogue.urlPdf) {
      window.open(catalogue.urlPdf, "_blank");
    }
  };

  const closeModal = () => {
    setModalState({ isOpen: false, mode: "create", catalogue: null });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">
          Gestion des Catalogues
        </h1>
        <button
          onClick={handleAdd}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Télécharger un Catalogue</span>
        </button>
      </div>

      {/* Search Bar */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Rechercher des catalogues..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Catalogues Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCatalogues.map((catalogue) => (
          <div
            key={catalogue.id}
            className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
          >
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
                      {catalogue.nom}
                    </h3>
                    <p className="text-sm text-gray-600">{catalogue.type}</p>
                  </div>
                </div>
                <span
                  className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    catalogue.isActive
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  {catalogue.isActive ? "Actif" : "Inactif"}
                </span>
              </div>

              <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                {catalogue.description}
              </p>

              <div className="space-y-2 mb-4">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-500">Type:</span>
                  <span className="text-gray-900">{catalogue.type}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-500 flex items-center">
                    <Calendar className="h-3 w-3 mr-1" />
                    Créé le:
                  </span>
                  <span className="text-gray-900">
                    {formatCatalogueDate(catalogue.dateCreation)}
                  </span>
                </div>
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={() => handleDownload(catalogue)}
                  className="flex-1 bg-green-50 text-green-600 px-3 py-2 rounded-lg hover:bg-green-100 transition-colors flex items-center justify-center space-x-1"
                >
                  <Download className="h-4 w-4" />
                  <span>Télécharger</span>
                </button>
                <button
                  onClick={() => handleView(catalogue)}
                  className="bg-gray-50 text-gray-600 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors"
                  title="Voir"
                >
                  <Eye className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleEdit(catalogue)}
                  className="bg-blue-50 text-blue-600 px-3 py-2 rounded-lg hover:bg-blue-100 transition-colors"
                  title="Modifier"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDelete(catalogue.id)}
                  className="bg-red-50 text-red-600 px-3 py-2 rounded-lg hover:bg-red-100 transition-colors"
                  title="Supprimer"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      <CatalogueModal
        isOpen={modalState.isOpen}
        onClose={closeModal}
        catalogue={modalState.catalogue}
        mode={modalState.mode}
      />
    </div>
  );
};

export default CatalogueManagement;
