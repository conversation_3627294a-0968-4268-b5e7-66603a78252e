import React, { useState, useRef } from "react";
import {
  Upload,
  FileText,
  Image,
  Download,
  Loader2,
  Check<PERSON>ircle,
  AlertCircle,
  Eye,
  Edit3,
  Trash2,
  Plus,
  X,
} from "lucide-react";
import {
  PDFExtractorService,
  ExtractedProduct,
} from "../../services/pdfExtractor";

interface ProductExtractionModalProps {
  isOpen: boolean;
  onClose: () => void;
  catalogueName: string;
  onProductsExtracted: (products: ExtractedProduct[]) => void;
}

interface ExtractionStep {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  current: boolean;
}

const ProductExtractionModal: React.FC<ProductExtractionModalProps> = ({
  isOpen,
  onClose,
  catalogueName,
  onProductsExtracted,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // États pour les fichiers
  const [pdfFile, setPdfFile] = useState<File | null>(null);
  const [productImages, setProductImages] = useState<File[]>([]);
  const [extractedProducts, setExtractedProducts] = useState<
    ExtractedProduct[]
  >([]);
  const [extractedText, setExtractedText] = useState("");

  // États pour l'édition
  const [editingProduct, setEditingProduct] = useState<ExtractedProduct | null>(
    null
  );
  const [showExtractedText, setShowExtractedText] = useState(false);

  const pdfInputRef = useRef<HTMLInputElement>(null);
  const imagesInputRef = useRef<HTMLInputElement>(null);

  const extractor = new PDFExtractorService();

  const steps: ExtractionStep[] = [
    {
      id: "upload-pdf",
      title: "Upload PDF",
      description: "Téléversez le catalogue PDF",
      completed: !!pdfFile,
      current: currentStep === 0,
    },
    {
      id: "upload-images",
      title: "Images produits",
      description: "Ajoutez les images des produits (optionnel)",
      completed: currentStep > 1,
      current: currentStep === 1,
    },
    {
      id: "extract",
      title: "Extraction",
      description: "Extraction automatique des produits",
      completed: extractedProducts.length > 0,
      current: currentStep === 2,
    },
    {
      id: "review",
      title: "Vérification",
      description: "Vérifiez et corrigez les données",
      completed: false,
      current: currentStep === 3,
    },
  ];

  const handlePDFUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type === "application/pdf") {
      setPdfFile(file);
      setError(null);
    } else {
      setError("Veuillez sélectionner un fichier PDF valide");
    }
  };

  const handleImagesUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const imageFiles = files.filter((file) => file.type.startsWith("image/"));
    setProductImages((prev) => [...prev, ...imageFiles]);
  };

  const removeImage = (index: number) => {
    setProductImages((prev) => prev.filter((_, i) => i !== index));
  };

  const startExtraction = async () => {
    if (!pdfFile) {
      setError("Veuillez d'abord téléverser un fichier PDF");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Étape 1: Extraction du PDF
      const result = await extractor.extractFromPDF(pdfFile);
      setExtractedText(result.extractedText);

      // Étape 2: Traitement des images si disponibles
      let finalProducts = result.products;
      if (productImages.length > 0) {
        const imageMap = await extractor.processProductImages(productImages);
        finalProducts = extractor.matchProductsWithImages(
          result.products,
          imageMap
        );
      }

      setExtractedProducts(finalProducts);
      setCurrentStep(3); // Aller à l'étape de vérification
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Erreur lors de l'extraction"
      );
    } finally {
      setLoading(false);
    }
  };

  const editProduct = (product: ExtractedProduct) => {
    setEditingProduct({ ...product });
  };

  const saveProductEdit = () => {
    if (editingProduct) {
      setExtractedProducts((prev) =>
        prev.map((p) => (p.id === editingProduct.id ? editingProduct : p))
      );
      setEditingProduct(null);
    }
  };

  const deleteProduct = (productId: string) => {
    setExtractedProducts((prev) => prev.filter((p) => p.id !== productId));
  };

  const addNewProduct = () => {
    const newProduct: ExtractedProduct = {
      id: `manual_${Date.now()}`,
      name: "Nouveau produit",
      description: "",
      characteristics: [],
      pageNumber: 1,
      confidence: 1.0,
    };
    setExtractedProducts((prev) => [...prev, newProduct]);
    setEditingProduct(newProduct);
  };

  const exportToJSON = () => {
    const jsonData = extractor.exportProductsToJSON(
      extractedProducts,
      catalogueName
    );
    const blob = new Blob([jsonData], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${catalogueName.replace(/[^a-z0-9]/gi, "_")}_products.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const finishExtraction = () => {
    onProductsExtracted(extractedProducts);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Extraction automatique de produits
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Catalogue: {catalogueName}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Steps */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                    step.completed
                      ? "bg-green-100 text-green-800"
                      : step.current
                      ? "bg-blue-100 text-blue-800"
                      : "bg-gray-100 text-gray-500"
                  }`}
                >
                  {step.completed ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    index + 1
                  )}
                </div>
                <div className="ml-2">
                  <p
                    className={`text-sm font-medium ${
                      step.current ? "text-blue-900" : "text-gray-900"
                    }`}
                  >
                    {step.title}
                  </p>
                  <p className="text-xs text-gray-500">{step.description}</p>
                </div>
                {index < steps.length - 1 && (
                  <div className="w-8 h-px bg-gray-300 mx-4" />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              {error}
            </div>
          )}

          {/* Étape 0: Upload PDF */}
          {currentStep === 0 && (
            <div className="text-center">
              <input
                ref={pdfInputRef}
                type="file"
                accept=".pdf"
                onChange={handlePDFUpload}
                className="hidden"
              />
              <div
                onClick={() => pdfInputRef.current?.click()}
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 hover:border-blue-500 cursor-pointer transition-colors"
              >
                <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-lg font-medium text-gray-900 mb-2">
                  Téléversez votre catalogue PDF
                </p>
                <p className="text-sm text-gray-500">
                  Cliquez pour sélectionner un fichier PDF
                </p>
              </div>

              {pdfFile && (
                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center justify-center">
                    <FileText className="h-6 w-6 text-green-600 mr-2" />
                    <span className="text-sm font-medium text-green-800">
                      {pdfFile.name} (
                      {(pdfFile.size / (1024 * 1024)).toFixed(2)} MB)
                    </span>
                  </div>
                  <button
                    onClick={() => setCurrentStep(1)}
                    className="mt-3 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    Continuer
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Étape 1: Upload Images */}
          {currentStep === 1 && (
            <div>
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Images des produits (optionnel)
                </h3>
                <p className="text-sm text-gray-600">
                  Ajoutez des images de produits pour améliorer la
                  correspondance automatique. Nommez vos fichiers selon le nom
                  du produit pour une meilleure reconnaissance.
                </p>
              </div>

              <input
                ref={imagesInputRef}
                type="file"
                accept="image/*"
                multiple
                onChange={handleImagesUpload}
                className="hidden"
              />

              <div
                onClick={() => imagesInputRef.current?.click()}
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-blue-500 cursor-pointer transition-colors mb-4"
              >
                <Image className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                <p className="text-center text-sm text-gray-600">
                  Cliquez pour ajouter des images
                </p>
              </div>

              {productImages.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  {productImages.map((image, index) => (
                    <div key={index} className="relative">
                      <img
                        src={URL.createObjectURL(image)}
                        alt={image.name}
                        className="w-full h-24 object-cover rounded-lg border"
                      />
                      <button
                        onClick={() => removeImage(index)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                      >
                        <Trash2 className="h-3 w-3" />
                      </button>
                      <p className="text-xs text-gray-600 mt-1 truncate">
                        {image.name}
                      </p>
                    </div>
                  ))}
                </div>
              )}

              <div className="flex space-x-3">
                <button
                  onClick={() => setCurrentStep(0)}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
                >
                  Retour
                </button>
                <button
                  onClick={() => setCurrentStep(2)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  Lancer l'extraction
                </button>
              </div>
            </div>
          )}

          {/* Étape 2: Extraction */}
          {currentStep === 2 && (
            <div className="text-center">
              {loading ? (
                <div>
                  <Loader2 className="h-12 w-12 mx-auto text-blue-600 animate-spin mb-4" />
                  <p className="text-lg font-medium text-gray-900 mb-2">
                    Extraction en cours...
                  </p>
                  <p className="text-sm text-gray-600">
                    Analyse du PDF et reconnaissance des produits
                  </p>
                </div>
              ) : (
                <div>
                  <button
                    onClick={startExtraction}
                    className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2 mx-auto"
                  >
                    <Upload className="h-5 w-5" />
                    <span>Commencer l'extraction</span>
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Étape 3: Vérification */}
          {currentStep === 3 && (
            <div>
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">
                    Produits extraits ({extractedProducts.length})
                  </h3>
                  <p className="text-sm text-gray-600">
                    Vérifiez et modifiez les informations si nécessaire
                  </p>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setShowExtractedText(!showExtractedText)}
                    className="px-3 py-2 text-sm bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors flex items-center space-x-1"
                  >
                    <Eye className="h-4 w-4" />
                    <span>Texte extrait</span>
                  </button>
                  <button
                    onClick={addNewProduct}
                    className="px-3 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center space-x-1"
                  >
                    <Plus className="h-4 w-4" />
                    <span>Ajouter</span>
                  </button>
                </div>
              </div>

              {showExtractedText && (
                <div className="mb-6 p-4 bg-gray-50 border rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">
                    Texte extrait du PDF:
                  </h4>
                  <pre className="text-xs text-gray-600 whitespace-pre-wrap max-h-40 overflow-y-auto">
                    {extractedText}
                  </pre>
                </div>
              )}

              <div className="space-y-4 max-h-96 overflow-y-auto">
                {extractedProducts.map((product) => (
                  <div
                    key={product.id}
                    className="border border-gray-200 rounded-lg p-4"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="font-medium text-gray-900">
                            {product.name}
                          </h4>
                          <span
                            className={`px-2 py-1 text-xs rounded-full ${
                              product.confidence > 0.8
                                ? "bg-green-100 text-green-800"
                                : product.confidence > 0.6
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {Math.round(product.confidence * 100)}% confiance
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">
                          {product.description}
                        </p>
                        {product.price && (
                          <p className="text-sm font-medium text-green-600">
                            Prix: {product.price}
                          </p>
                        )}
                        {product.sku && (
                          <p className="text-sm text-gray-500">
                            SKU: {product.sku}
                          </p>
                        )}
                        {product.characteristics.length > 0 && (
                          <div className="mt-2">
                            <p className="text-xs font-medium text-gray-700">
                              Caractéristiques:
                            </p>
                            <ul className="text-xs text-gray-600 list-disc list-inside">
                              {product.characteristics
                                .slice(0, 3)
                                .map((char, index) => (
                                  <li key={index}>{char}</li>
                                ))}
                            </ul>
                          </div>
                        )}
                      </div>

                      {product.imageUrl && (
                        <img
                          src={product.imageUrl}
                          alt={product.name}
                          className="w-16 h-16 object-cover rounded-lg ml-4"
                        />
                      )}

                      <div className="flex flex-col space-y-1 ml-4">
                        <button
                          onClick={() => editProduct(product)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                        >
                          <Edit3 className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => deleteProduct(product.id)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        {currentStep === 3 && (
          <div className="flex justify-between items-center p-6 border-t border-gray-200">
            <button
              onClick={exportToJSON}
              className="px-4 py-2 text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors flex items-center space-x-2"
            >
              <Download className="h-4 w-4" />
              <span>Exporter JSON</span>
            </button>

            <div className="flex space-x-3">
              <button
                onClick={() => setCurrentStep(1)}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
              >
                Retour
              </button>
              <button
                onClick={finishExtraction}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                disabled={extractedProducts.length === 0}
              >
                Finaliser
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Modal d'édition de produit */}
      {editingProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                Modifier le produit
              </h3>
              <button
                onClick={() => setEditingProduct(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="p-4 overflow-y-auto max-h-[calc(80vh-120px)]">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Nom du produit
                  </label>
                  <input
                    type="text"
                    value={editingProduct.name}
                    onChange={(e) =>
                      setEditingProduct({
                        ...editingProduct,
                        name: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={editingProduct.description}
                    onChange={(e) =>
                      setEditingProduct({
                        ...editingProduct,
                        description: e.target.value,
                      })
                    }
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Prix
                    </label>
                    <input
                      type="text"
                      value={editingProduct.price || ""}
                      onChange={(e) =>
                        setEditingProduct({
                          ...editingProduct,
                          price: e.target.value,
                        })
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      SKU
                    </label>
                    <input
                      type="text"
                      value={editingProduct.sku || ""}
                      onChange={(e) =>
                        setEditingProduct({
                          ...editingProduct,
                          sku: e.target.value,
                        })
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Caractéristiques (une par ligne)
                  </label>
                  <textarea
                    value={editingProduct.characteristics.join("\n")}
                    onChange={(e) =>
                      setEditingProduct({
                        ...editingProduct,
                        characteristics: e.target.value
                          .split("\n")
                          .filter((c) => c.trim()),
                      })
                    }
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 p-4 border-t border-gray-200">
              <button
                onClick={() => setEditingProduct(null)}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
              >
                Annuler
              </button>
              <button
                onClick={saveProductEdit}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Sauvegarder
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductExtractionModal;
