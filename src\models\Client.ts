import { Timestamp } from 'firebase/firestore';
import {
  BaseEntity,
  ValidationResult,
  ValidationError,
  CreateEntity,
  UpdateEntity,
  isValidEmail,
  isValidPhone,
  sanitizeString,
  VALIDATION_LIMITS
} from './common';

// ============================================================================
// CLIENT INTERFACES
// ============================================================================

/**
 * Main Client interface - simplified to match database schema
 */
export interface Client extends BaseEntity {
  // Basic Information
  codeClient: string; // Code client unique
  nomClient: string; // Nom du client
  categorie: string; // Catégorie du client

  // Contact Information
  email: string; // Email du client
  telephone: string; // Téléphone du client

  // Address Information
  adresse: string; // Adresse du client

  // Business Information
  matriculeFiscale: string; // Matricule fiscale
  modeReglement: string; // Mode de règlement

  // Dates
  dateCreation: Timestamp; // Date de création
}

// ============================================================================
// CLIENT FORM INTERFACES
// ============================================================================

/**
 * Client form data interface (for forms)
 */
export interface ClientFormData {
  codeClient: string;
  nomClient: string;
  categorie: string;
  email: string;
  telephone: string;
  adresse: string;
  matriculeFiscale: string;
  modeReglement: string;
}

/**
 * Client search/filter interface
 */
export interface ClientSearchFilters {
  nomClient?: string;
  codeClient?: string;
  email?: string;
  telephone?: string;
  categorie?: string;
  matriculeFiscale?: string;
  modeReglement?: string;
  dateCreationAfter?: Timestamp;
  dateCreationBefore?: Timestamp;
}

// ============================================================================
// TYPE ALIASES
// ============================================================================

export type CreateClientData = CreateEntity<Client>;
export type UpdateClientData = UpdateEntity<Client>;
export type ClientWithId = Client & Required<Pick<Client, 'id'>>;

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate client form data
 */
export const validateClientData = (data: Partial<ClientFormData>): ValidationResult => {
  const errors: ValidationError[] = [];

  // Required fields validation
  if (!data.codeClient || data.codeClient.trim().length === 0) {
    errors.push({
      field: 'codeClient',
      message: 'Le code client est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.nomClient || data.nomClient.trim().length === 0) {
    errors.push({
      field: 'nomClient',
      message: 'Le nom du client est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.email || data.email.trim().length === 0) {
    errors.push({
      field: 'email',
      message: 'L\'email est requis',
      code: 'REQUIRED'
    });
  } else if (!isValidEmail(data.email)) {
    errors.push({
      field: 'email',
      message: 'Format d\'email invalide',
      code: 'INVALID_FORMAT'
    });
  }

  if (!data.telephone || data.telephone.trim().length === 0) {
    errors.push({
      field: 'telephone',
      message: 'Le téléphone est requis',
      code: 'REQUIRED'
    });
  } else if (!isValidPhone(data.telephone)) {
    errors.push({
      field: 'telephone',
      message: 'Format de téléphone invalide',
      code: 'INVALID_FORMAT'
    });
  }

  if (!data.adresse || data.adresse.trim().length === 0) {
    errors.push({
      field: 'adresse',
      message: 'L\'adresse est requise',
      code: 'REQUIRED'
    });
  }

  // Length validations
  if (data.nomClient && data.nomClient.length > VALIDATION_LIMITS.NAME_MAX_LENGTH) {
    errors.push({
      field: 'nomClient',
      message: `Le nom ne peut pas dépasser ${VALIDATION_LIMITS.NAME_MAX_LENGTH} caractères`,
      code: 'INVALID_LENGTH'
    });
  }

  if (data.email && data.email.length > VALIDATION_LIMITS.EMAIL_MAX_LENGTH) {
    errors.push({
      field: 'email',
      message: `L'email ne peut pas dépasser ${VALIDATION_LIMITS.EMAIL_MAX_LENGTH} caractères`,
      code: 'INVALID_LENGTH'
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Sanitize client data before saving
 */
export const sanitizeClientData = (data: ClientFormData): ClientFormData => {
  return {
    ...data,
    codeClient: sanitizeString(data.codeClient),
    nomClient: sanitizeString(data.nomClient),
    categorie: sanitizeString(data.categorie),
    email: data.email.toLowerCase().trim(),
    telephone: data.telephone.replace(/[\s\-\(\)]/g, ''),
    adresse: sanitizeString(data.adresse),
    matriculeFiscale: sanitizeString(data.matriculeFiscale),
    modeReglement: sanitizeString(data.modeReglement)
  };
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get client display name
 */
export const getClientDisplayName = (client: Client): string => {
  return client.nomClient;
};

/**
 * Get client full address
 */
export const getClientFullAddress = (client: Client): string => {
  return client.adresse || 'Adresse non renseignée';
};

/**
 * Format client code
 */
export const formatClientCode = (codeClient: string): string => {
  return codeClient.toUpperCase();
};
