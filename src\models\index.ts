// ============================================================================
// MODELS INDEX - Export all models and types
// ============================================================================

// Common types and utilities
export * from './common';

// Entity models
export * from './Client';
export * from './Commercial';
export * from './Product';
export * from './Order';
export * from './Merchandizer';
export * from './Catalogue';
export * from './Mission';
export * from './Devis';

// Re-export commonly used types for convenience
export type {
  // Common
  BaseEntity,
  ValidationResult,
  ValidationError,
  ApiResponse,
  PaginatedResponse,
  SearchParams,
  CreateEntity,
  UpdateEntity,
  EntityWithId,
  
  // Client
  Client,
  ClientFormData,
  ClientSearchFilters,
  CreateClientData,
  UpdateClientData,
  ClientWithId,
  
  // Commercial
  Commercial,
  CommercialFormData,
  CommercialSearchFilters,
  CreateCommercialData,
  UpdateCommercialData,
  CommercialWithId,
  CommercialAccount,
  
  // Product
  Product,
  ProductFormData,
  ProductSearchFilters,
  CreateProductData,
  UpdateProductData,
  ProductWithId,
  
  // Order
  Order,
  OrderFormData,
  OrderSearchFilters,
  CreateOrderData,
  UpdateOrderData,
  OrderWithId,
  OrderLineItem,

  // Devis
  Devis,
  DevisFormData,
  DevisSearchFilters,
  CreateDevisData,
  UpdateDevisData,
  DevisWithId,
  DevisLineItem,

  // Merchandizer
  Merchandizer,
  MerchandizerFormData,
  MerchandizerSearchFilters,
  CreateMerchandizerData,
  UpdateMerchandizerData,
  MerchandizerWithId,
  Store,
  VisitReport,
  MerchandizerAccount,
  
  // Catalogue
  Catalogue,
  CatalogueFormData,
  CatalogueSearchFilters,
  CreateCatalogueData,
  UpdateCatalogueData,
  CatalogueWithId,
  CatalogueVersion,
  CatalogueAnalytics
} from './common';

// Re-export commonly used enums
export {
  // Common enums
  EntityStatus,
  OrderStatus,
  Priority
} from './common';

export {
  // Client enums
  ClientType,
  ClientCategory,
  PaymentTerms
} from './Client';

export {
  // Commercial enums
  CommercialRole,
  EmploymentType,
  TerritoryType,
  CommercialMobilePermission
} from './Commercial';

export {
  // Product enums
  ProductType,
  ProductAvailability,
  ProductCondition
} from './Product';

export {
  // Order enums
  OrderType,
  PaymentStatus,
  ShippingMethod,
  OrderSource
} from './Order';

export {
  // Devis enums
  DevisStatus,
  DevisPriority
} from './Devis';

export {
  // Merchandizer enums
  MerchandizerType,
  VisitType,
  StoreType,
  MobilePermission,
  AccountStatus
} from './Merchandizer';

export {
  // Catalogue enums
  CatalogueType,
  CatalogueFormat,
  DistributionChannel
} from './Catalogue';

// Re-export validation functions
export {
  // Client validation
  validateClientData,
  sanitizeClientData
} from './Client';

export {
  // Commercial validation
  validateCommercialData,
  sanitizeCommercialData
} from './Commercial';

export {
  // Product validation
  validateProductData,
  sanitizeProductData
} from './Product';

export {
  // Order validation
  validateOrderData,
  sanitizeOrderData
} from './Order';

export {
  // Devis validation
  validateDevisData,
  sanitizeDevisData
} from './Devis';

export {
  // Merchandizer validation
  validateMerchandizerData,
  sanitizeMerchandizerData
} from './Merchandizer';

export {
  // Catalogue validation
  validateCatalogueData,
  sanitizeCatalogueData
} from './Catalogue';

// Import account utilities from specific models
export {
  // Merchandizer account utilities
  canAccountLogin,
  needsPasswordChange,
  getDefaultMerchandizerPermissions,
  createDefaultAccountSettings,
  isValidUsername,
  generateTemporaryPassword,
  isDeviceRegistered,
  getActiveDevicesCount,
} from './Merchandizer';

export {
  // Commercial account utilities
  canCommercialAccountLogin,
  needsCommercialPasswordChange,
  getDefaultCommercialPermissions,
  createDefaultCommercialAccountSettings,
  canApproveDiscount,
  orderNeedsApproval,
  canAccessClient,
} from './Commercial';

// Re-export utility functions
export {
  // Common utilities
  generateId,
  formatDate,
  formatDateTime,
  isValidEmail,
  isValidPhone,
  formatCurrency,
  formatCurrencyWithDecimals,
  sanitizeString,
  deepClone,
  isEmpty
} from './common';

export {
  // Client utilities
  getClientDisplayName,
  getClientFullAddress,
  isClientPremium,
  calculateClientLifetimeValue,
  getClientRiskLevel
} from './Client';

export {
  // Commercial utilities
  getCommercialFullName,
  calculatePerformanceScore,
  getPerformanceRating,
  isMeetingTargets as isCommercialMeetingTargets,
  calculateCommissionEarned
} from './Commercial';

export {
  // Product utilities
  calculateProfitMargin,
  isLowStock,
  isOutOfStock,
  getDisplayPrice,
  calculateVolume,
  generateProductSlug,
  needsReorder,
  getAvailabilityStatus
} from './Product';

export {
  // Order utilities
  calculateOrderSubtotal,
  calculateOrderTotal,
  generateOrderNumber,
  canCancelOrder,
  canModifyOrder,
  getOrderStatusText,
  isOrderOverdue,
  calculateOrderProfit
} from './Order';

export {
  // Devis utilities
  calculateDevisSubtotal,
  calculateDevisTotal,
  generateDevisNumber,
  canModifyDevis,
  getDevisStatusText,
  isDevisExpired,
  formatDevisDate
} from './Devis';

export {
  // Merchandizer utilities
  getMerchandizerFullName,
  calculateMerchandizerPerformanceScore,
  getMerchandizerPerformanceRating,
  isMeetingTargets as isMerchandizerMeetingTargets,
  getTotalStoresManaged,
  getNextScheduledVisit,
  calculateAverageVisitDuration
} from './Merchandizer';

export {
  // Catalogue utilities
  generateSlug,
  isCatalogueExpired,
  isCataloguePublished,
  getCatalogueFileExtension,
  calculateEngagementScore,
  getCataloguePerformanceRating,
  formatFileSize,
  canDownloadCatalogue
} from './Catalogue';

// Constants
export {
  DEFAULT_PAGINATION,
  VALIDATION_LIMITS,
  FILE_UPLOAD
} from './common';
