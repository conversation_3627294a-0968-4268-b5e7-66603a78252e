import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User,
  updateProfile,
  sendPasswordResetEmail
} from "firebase/auth";
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { auth, db } from "./config";
import { doc, setDoc, getDoc, Timestamp } from "firebase/firestore";

export interface UserProfile {
  uid: string;
  email: string;
  displayName: string;
  role: 'admin' | 'commercial' | 'merchandiser' | 'user';
  employeeId?: string;
  department?: string;
  territory?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export class AuthService {
  // Sign up new user
  static async signUp(email: string, password: string, displayName: string, role: 'admin' | 'commercial' | 'merchandiser' | 'user' = 'user', additionalData?: Partial<UserProfile>) {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Update user profile
      await updateProfile(user, {
        displayName: displayName
      });

      // Create user profile in Firestore (only include defined fields)
      const userProfile: UserProfile = {
        uid: user.uid,
        email: user.email!,
        displayName: displayName,
        role: role,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      // Only add optional fields if they are defined
      if (additionalData?.employeeId) {
        userProfile.employeeId = additionalData.employeeId;
      }
      if (additionalData?.department) {
        userProfile.department = additionalData.department;
      }
      if (additionalData?.territory) {
        userProfile.territory = additionalData.territory;
      }

      await setDoc(doc(db, 'users', user.uid), userProfile);

      return { user, userProfile };
    } catch (error) {
      console.error('Error signing up:', error);
      // If user was created but profile failed, we should clean up
      if (auth.currentUser) {
        try {
          await auth.currentUser.delete();
        } catch (deleteError) {
          console.error('Error cleaning up user after profile creation failure:', deleteError);
        }
      }
      throw error;
    }
  }

  // Note: Employee account creation through admin interface has been removed
  // due to Firebase v9+ limitations with secondary app instances.
  // Commercial and merchandiser accounts should be created through the normal signup process
  // and then have their roles updated by an admin.


  
  // Sign in existing user
  static async signIn(email: string, password: string) {
    try {
      console.log("AuthService: Tentative de connexion pour:", email);
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      console.log("AuthService: Connexion réussie pour:", user.email);
      
      // Get user profile from Firestore
      const userDoc = await getDoc(doc(db, 'users', user.uid));
      const userProfile = userDoc.exists() ? userDoc.data() as UserProfile : null;
      
      console.log("AuthService: Profil utilisateur récupéré:", userProfile ? "Oui" : "Non");
      
      return { user, userProfile };
    } catch (error) {
      console.error('AuthService: Erreur lors de la connexion:', error);
      
      // Log des détails de l'erreur pour debugging
      if (error && typeof error === 'object' && 'code' in error) {
        console.error('AuthService: Code d\'erreur:', (error as { code: string }).code);
      }
      if (error && typeof error === 'object' && 'message' in error) {
        console.error('AuthService: Message d\'erreur:', (error as { message: string }).message);
      }
      
      throw error;
    }
  }
  
  // Sign out user
  static async signOut() {
    try {
      await signOut(auth);
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  }
  
  // Reset password
  static async resetPassword(email: string) {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error) {
      console.error('Error sending reset email:', error);
      throw error;
    }
  }
  
  // Get user profile
  static async getUserProfile(uid: string): Promise<UserProfile | null> {
    try {
      const userDoc = await getDoc(doc(db, 'users', uid));
      return userDoc.exists() ? userDoc.data() as UserProfile : null;
    } catch (error) {
      console.error('Error getting user profile:', error);
      return null;
    }
  }
  
  // Update user profile
  static async updateUserProfile(uid: string, updates: Partial<UserProfile>) {
    try {
      // Clean updates to remove undefined values
      const cleanedUpdates: Record<string, any> = {
        updatedAt: Timestamp.now()
      };

      // Only add defined values
      Object.entries(updates).forEach(([key, value]) => {
        if (value !== undefined) {
          cleanedUpdates[key] = value;
        }
      });

      await setDoc(doc(db, 'users', uid), cleanedUpdates, { merge: true });
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }
  
  // Auth state observer
  static onAuthStateChanged(callback: (user: User | null) => void) {
    return onAuthStateChanged(auth, callback);
  }

  // Update current user role (for debugging/admin purposes)
  static async updateCurrentUserRole(role: 'admin' | 'commercial' | 'merchandiser' | 'user') {
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error('No user is currently logged in');
      }

      await this.updateUserProfile(user.uid, { role });
      console.log(`User role updated to: ${role}`);
    } catch (error) {
      console.error('Error updating user role:', error);
      throw error;
    }
  }
}

export default AuthService;
