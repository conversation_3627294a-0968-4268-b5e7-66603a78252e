import { useAuth } from './useAuth';

export type UserRole = 'admin' | 'commercial' | 'merchandiser' | 'user';

export const useRoleAccess = () => {
  const { userProfile } = useAuth();
  
  const hasRole = (requiredRoles: UserRole | UserRole[]): boolean => {
    if (!userProfile) return false;
    
    const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
    return roles.includes(userProfile.role as UserRole);
  };
  
  const isAdmin = (): boolean => {
    return hasRole('admin');
  };
  
  const isCommercial = (): boolean => {
    return hasRole('commercial');
  };
  
  const isMerchandiser = (): boolean => {
    return hasRole('merchandiser');
  };
  
  const canAccessSection = (section: string): boolean => {
    const role = userProfile?.role as UserRole;
    
    switch (section) {
      case 'dashboard':
        return hasRole(['admin', 'commercial', 'merchandiser']);
      case 'commercials':
      case 'merchandizers':
      case 'catalogues':
      case 'test-extraction':
        return hasRole('admin');
      case 'clients':
      case 'orders':
        return hasRole(['admin', 'commercial']);
      case 'products':
      case 'missions':
      case 'mission-calendar':
        return hasRole(['admin', 'commercial', 'merchandiser']);
      default:
        return false;
    }
  };
  
  return {
    userRole: userProfile?.role as UserRole,
    hasRole,
    isAdmin,
    isCommercial,
    isMerchandiser,
    canAccessSection,
  };
};
