import React from "react";
import {
  LayoutDashboard,
  Users,
  UserCheck,
  Package,
  ShoppingCart,
  FileText,
  ClipboardList,
  Calendar,
  MapPin,
  LogOut,
  User,
  Zap,
} from "lucide-react";
import { useAuth } from "../../hooks/useAuth";
import Logo from "../ui/Logo";

type ActiveSection =
  | "dashboard"
  | "commercials"
  | "clients"
  | "products"
  | "merchandizers"
  | "catalogues"
  | "orders"
  | "missions"
  | "mission-calendar"
  | "test-extraction";

interface SidebarProps {
  activeSection: ActiveSection;
  setActiveSection: (section: ActiveSection) => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  activeSection,
  setActiveSection,
}) => {
  const { userProfile, signOut } = useAuth();

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  // Define menu items based on user role
  const getMenuItemsForRole = (role: string) => {
    const baseItems = [
      {
        id: "dashboard",
        label: "Tableau de bord",
        icon: LayoutDashboard,
        description: "Vue d'ensemble des données",
        roles: ["admin", "commercial", "merchandiser"],
      },
      {
        id: "clients",
        label: "Clients",
        icon: Users,
        description: "Base de données clients",
        roles: ["admin", "commercial"],
      },
      {
        id: "products",
        label: "Produits",
        icon: Package,
        description: "Catalogue produits",
        roles: ["admin", "commercial", "merchandiser"],
      },
      {
        id: "orders",
        label: "Commandes",
        icon: ClipboardList,
        description: "Suivi des commandes",
        roles: ["admin", "commercial"],
      },
      {
        id: "missions",
        label: "Missions",
        icon: MapPin,
        description: "Gestion des missions",
        roles: ["admin", "commercial", "merchandiser"],
      },
      {
        id: "mission-calendar",
        label: "Calendrier Missions",
        icon: Calendar,
        description: "Calendrier des missions",
        roles: ["admin", "commercial", "merchandiser"],
      },
    ];

    const adminOnlyItems = [
      {
        id: "commercials",
        label: "Commerciaux",
        icon: UserCheck,
        description: "Gestion des commerciaux",
        roles: ["admin"],
      },
      {
        id: "merchandizers",
        label: "Merchandiseurs",
        icon: ShoppingCart,
        description: "Équipe merchandising",
        roles: ["admin"],
      },
      {
        id: "catalogues",
        label: "Catalogues",
        icon: FileText,
        description: "Gestion des catalogues",
        roles: ["admin"],
      },
      {
        id: "test-extraction",
        label: "Test Extraction",
        icon: Zap,
        description: "Test d'extraction de produits",
        roles: ["admin"],
      },
    ];

    const allItems = [...baseItems, ...adminOnlyItems];
    return allItems.filter((item) => item.roles.includes(role));
  };

  const menuItems = getMenuItemsForRole(userProfile?.role || "user");

  return (
    <div className="w-80 bg-white shadow-xl border-r border-gray-200 flex flex-col h-full">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-indigo-600">
        <div className="flex items-center space-x-3">
          <Logo size="md" variant="default" />
          <div>
            <h1 className="text-xl font-bold text-white">VitaBrosse</h1>
            <p className="text-blue-100 text-sm">Administration</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 mt-6 px-4">
        <ul className="space-y-3">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeSection === item.id;

            return (
              <li key={item.id}>
                <button
                  onClick={() => setActiveSection(item.id)}
                  className={`w-full group flex items-center space-x-4 px-4 py-4 rounded-xl transition-all duration-200 ${
                    isActive
                      ? "bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg transform scale-105"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900 hover:shadow-md"
                  }`}
                >
                  <Icon
                    className={`h-6 w-6 ${
                      isActive
                        ? "text-white"
                        : "text-gray-500 group-hover:text-blue-500"
                    }`}
                  />
                  <div className="flex-1 text-left">
                    <span className="font-semibold text-base">
                      {item.label}
                    </span>
                    <p
                      className={`text-sm ${
                        isActive ? "text-blue-100" : "text-gray-500"
                      }`}
                    >
                      {item.description}
                    </p>
                  </div>
                  {isActive && (
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  )}
                </button>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* User Profile Section */}
      <div className="p-6 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center shadow-lg">
              <User className="h-6 w-6 text-white" />
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-bold text-gray-900 truncate">
              {userProfile?.displayName || "Utilisateur"}
            </p>
            <p className="text-xs text-gray-500 truncate">
              {userProfile?.email || "<EMAIL>"}
            </p>
            <p className="text-xs text-blue-600 font-medium truncate">
              {userProfile?.role || "Administrateur"}
            </p>
          </div>
        </div>

        {/* Sign Out Button */}
        <button
          onClick={handleSignOut}
          className="w-full mt-4 flex items-center justify-center space-x-3 px-4 py-3 text-gray-600 hover:bg-red-50 hover:text-red-700 rounded-xl transition-all duration-200 border border-gray-200 hover:border-red-200"
        >
          <LogOut className="h-5 w-5" />
          <span className="font-semibold">Déconnexion</span>
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
