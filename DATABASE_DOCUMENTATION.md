# Database Documentation - VitaBrosse Commercial Management System

## Overview

This document provides comprehensive documentation of all database tables, fields, and their relationships in the VitaBrosse commercial management application. The application uses Firebase Firestore as the database backend with TypeScript models for type safety.

## Database Architecture

### Technology Stack
- **Database**: Firebase Firestore (NoSQL Document Database)
- **Type System**: TypeScript with comprehensive models
- **Currency**: Tunisian Dinar (DT)
- **Authentication**: Firebase Auth

### Collection Structure

The database consists of the following main collections:

## 📋 Collections Overview

| Collection | Purpose | Document Count | Key Relationships |
|------------|---------|----------------|-------------------|
| `users` | Authentication & user profiles | Variable | → `commercials`, `merchandizers` |
| `clients` | Customer/Client management | Variable | → `orders`, `missions` |
| `commercials` | Sales representatives | Variable | → `clients`, `orders` |
| `merchandizers` | Field merchandisers | Variable | → `missions`, `clients` |
| `products` | Product catalog | Variable | → `orders` |
| `orders` | Sales orders | Variable | ← `clients`, `commercials`, `products` |
| `catalogues` | Digital catalogs | Variable | → `products` |
| `missions` | Field missions | Variable | ← `merchandizers`, `clients` |

---

## 👤 Users Collection (`users`)

**Purpose**: Stores authentication profiles and user account information for Firebase Auth integration. This collection manages user roles, permissions, and profile data for the application's authentication system.

### Core Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `uid` | string | ✅ | Firebase Auth UID (document ID) |
| `email` | string | ✅ | User email address |
| `displayName` | string | ✅ | User display name |
| `role` | enum | ✅ | User role in the system |
| `employeeId` | string | ❌ | Employee identification number |
| `department` | string | ❌ | Department assignment |
| `territory` | string | ❌ | Territory assignment |
| `createdAt` | Timestamp | Auto | Account creation timestamp |
| `updatedAt` | Timestamp | Auto | Last update timestamp |

### Role Enum

**role** (User Role):
- `admin` - System administrator with full access
- `commercial` - Sales representative
- `merchandiser` - Field merchandiser
- `user` - Basic user (default)

### Key Features

1. **Firebase Auth Integration**:
   - Document ID matches Firebase Auth UID
   - Synchronized with Firebase Authentication
   - Automatic profile creation on signup

2. **Role-Based Access Control**:
   - Determines user permissions in the application
   - Controls access to different features and data
   - Supports hierarchical access levels

3. **Profile Management**:
   - Stores additional user information beyond Firebase Auth
   - Links to employee records (commercials/merchandizers)
   - Territory and department assignments

### Usage Patterns

- **Authentication Flow**: Created automatically when users sign up
- **Role Assignment**: Admins can update user roles
- **Profile Linking**: Links to `commercials` or `merchandizers` collections via `employeeId`
- **Permission Checking**: Used to determine user capabilities in the app

### Account Creation Process (FIXED)

**For Commercials:**
```
✅ FIXED: Admin creates commercial →
   1. Creates Firebase Auth account in 'users' collection
   2. Creates business record in 'commercials' collection
   3. Links both records via employeeId and userId
   4. Commercial can now login with email/password
```

**For Merchandizers:**
```
✅ FIXED: Admin creates merchandizer →
   1. Creates Firebase Auth account in 'users' collection
   2. Creates business record in 'merchandizers' collection
   3. Links both records via employeeId and userId
   4. Merchandizer can now login with email/password
```

### Data Synchronization

**AccountSyncService** maintains consistency between collections:
- `syncUserWithCommercial()` - Links user and commercial records
- `syncUserWithMerchandizer()` - Links user and merchandizer records
- `getCompleteEmployeeProfile()` - Gets unified employee data
- `updateEmployeeData()` - Updates data across all collections
- `validateDataConsistency()` - Checks for data mismatches
- `fixDataInconsistencies()` - Repairs data conflicts

### Security Notes

- Documents are secured by Firebase Auth rules
- Users can only read/update their own profile
- Role changes require admin privileges
- No sensitive data stored (passwords handled by Firebase Auth)

---

## 🏢 Clients Collection (`clients`)

**Purpose**: Stores customer/client information including contact details, financial info, and business relationships.

### Core Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | Auto | Firestore document ID |
| `codeClient` | string | ✅ | Unique client code |
| `nomClient` | string | ✅ | Client name |
| `firstName` | string | ❌ | First name (for individuals) |
| `lastName` | string | ❌ | Last name (for individuals) |
| `company` | string | ✅ | Company name |
| `email` | string | ✅ | Email address |
| `tel` | string | ✅ | Fixed phone number |
| `fax` | string | ❌ | Fax number |
| `portable` | string | ❌ | Mobile phone number |
| `adresse` | string | ✅ | Primary address |
| `matriculeFiscale` | string | ❌ | Tax registration number |
| `modeReglement` | enum | ✅ | Payment method |
| `status` | enum | ✅ | Client status |
| `createdAt` | Timestamp | Auto | Creation timestamp |
| `updatedAt` | Timestamp | Auto | Last update timestamp |

### Enums

**modeReglement** (Payment Method):
- `especes` - Cash
- `cheque` - Check
- `virement` - Bank transfer
- `carte_bancaire` - Credit card
- `traite` - Draft
- `credit` - Credit

**status**:
- `active` - Active client
- `inactive` - Inactive client

### Extended Fields (From TypeScript Models)

| Field | Type | Description |
|-------|------|-------------|
| `clientType` | enum | individual, company, government, non_profit |
| `category` | enum | premium, standard, basic, vip |
| `website` | string | Client website URL |
| `billingAddress` | Address | Billing address object |
| `shippingAddress` | Address | Shipping address object |
| `industry` | string | Industry sector |
| `companySize` | enum | small, medium, large, enterprise |
| `annualRevenue` | number | Annual revenue in DT |
| `assignedCommercialId` | string | Assigned commercial ID |
| `assignedCommercialName` | string | Assigned commercial name |
| `financialInfo` | object | Credit limit, balance, payment terms |
| `preferences` | object | Communication preferences |
| `stats` | object | Order statistics and metrics |
| `priority` | enum | low, medium, high |
| `tags` | string[] | Client tags |
| `notes` | string | Additional notes |
| `audit` | object | Audit trail information |

---

## 👨‍💼 Commercials Collection (`commercials`)

**Purpose**: Manages sales representatives, their territories, and account information.

### Core Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | Auto | Firestore document ID |
| `firstName` | string | ✅ | First name |
| `lastName` | string | ✅ | Last name |
| `name` | string | ✅ | Full name |
| `email` | string | ✅ | Email address |
| `phone` | string | ✅ | Phone number |
| `mobile` | string | ❌ | Mobile number |
| `employeeId` | string | ✅ | Employee ID |
| `role` | string | ✅ | Job role |
| `department` | string | ✅ | Department |
| `territory` | string | ✅ | Assigned territory |
| `status` | enum | ✅ | Employment status |
| `account` | object | ✅ | Mobile app account info |
| `createdAt` | Timestamp | Auto | Creation timestamp |
| `updatedAt` | Timestamp | Auto | Last update timestamp |

### Account Object Structure

| Field | Type | Description |
|-------|------|-------------|
| `username` | string | Login username |
| `email` | string | Account email |
| `temporaryPassword` | string | Temporary password |
| `accountStatus` | enum | active, inactive, suspended, pending_activation, locked |
| `isFirstLogin` | boolean | First login flag |
| `mustChangePassword` | boolean | Password change required |
| `permissions` | string[] | User permissions |
| `canAccessOffline` | boolean | Offline access permission |
| `maxOfflineDays` | number | Maximum offline days |
| `requireBiometric` | boolean | Biometric authentication required |
| `sessionTimeout` | number | Session timeout in minutes |
| `allowMultipleDevices` | boolean | Multiple device access |
| `canViewAllClients` | boolean | View all clients permission |
| `canEditPricing` | boolean | Edit pricing permission |
| `maxDiscountPercentage` | number | Maximum discount allowed |
| `requireApprovalAbove` | number | Approval threshold amount |
| `createdBy` | string | Account creator |
| `createdDate` | Timestamp | Account creation date |

---

## 👨‍🔧 Merchandizers Collection (`merchandizers`)

**Purpose**: Manages field merchandisers who perform client visits and missions.

### Core Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | Auto | Firestore document ID |
| `firstName` | string | ✅ | First name |
| `lastName` | string | ✅ | Last name |
| `name` | string | ✅ | Full name |
| `email` | string | ✅ | Email address |
| `phone` | string | ✅ | Phone number |
| `mobile` | string | ❌ | Mobile number |
| `employeeId` | string | ❌ | Employee ID |
| `type` | string | ✅ | Merchandiser type |
| `department` | string | ❌ | Department |
| `territory` | string | ✅ | Assigned territory |
| `stores` | number | ✅ | Number of stores managed |
| `lastVisit` | Timestamp | ✅ | Last visit date |
| `performance` | number | ✅ | Performance score |
| `status` | enum | ✅ | Employment status |
| `account` | object | ✅ | Mobile app account info |
| `createdAt` | Timestamp | Auto | Creation timestamp |
| `updatedAt` | Timestamp | Auto | Last update timestamp |

### Account Object Structure
Similar to Commercials account structure but without pricing permissions.

---

## 📦 Products Collection (`products`)

**Purpose**: Product catalog with pricing, inventory, and specifications.

### Core Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | Auto | Firestore document ID |
| `name` | string | ✅ | Product name |
| `description` | string | ✅ | Product description |
| `price` | number | ✅ | Price in DT |
| `category` | string | ✅ | Product category |
| `stock` | number | ✅ | Stock quantity |
| `imageUrl` | string | ❌ | Product image URL |
| `createdAt` | Timestamp | Auto | Creation timestamp |
| `updatedAt` | Timestamp | Auto | Last update timestamp |

### Extended Fields (From TypeScript Models)

| Field | Type | Description |
|-------|------|-------------|
| `shortDescription` | string | Brief description |
| `sku` | string | Stock Keeping Unit |
| `barcode` | string | Product barcode |
| `type` | enum | physical, digital, service, subscription |
| `subcategory` | string | Product subcategory |
| `tags` | string[] | Product tags |
| `pricing` | object | Detailed pricing information |
| `inventory` | object | Inventory management |
| `dimensions` | object | Physical dimensions |
| `condition` | enum | new, used, refurbished |
| `specifications` | object | Technical specifications |
| `media` | object | Images, videos, documents |
| `seo` | object | SEO metadata |
| `analytics` | object | Performance analytics |
| `availability` | enum | in_stock, out_of_stock, pre_order, discontinued |
| `isActive` | boolean | Product active status |
| `isFeatured` | boolean | Featured product flag |

---

## 📋 Orders Collection (`orders`)

**Purpose**: Sales orders created by commercials for clients.

### Core Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | Auto | Firestore document ID |
| `orderNumber` | string | ✅ | Unique order number |
| `clientId` | string | ✅ | Reference to client |
| `clientName` | string | ✅ | Client name (denormalized) |
| `commercialId` | string | ✅ | Reference to commercial |
| `commercialName` | string | ✅ | Commercial name (denormalized) |
| `products` | array | ✅ | Array of ordered products |
| `totalAmount` | number | ✅ | Total amount in DT |
| `status` | enum | ✅ | Order status |
| `orderDate` | Timestamp | ✅ | Order date |
| `deliveryDate` | Timestamp | ❌ | Delivery date |
| `createdAt` | Timestamp | Auto | Creation timestamp |
| `updatedAt` | Timestamp | Auto | Last update timestamp |

### Products Array Structure

Each product in the `products` array contains:

| Field | Type | Description |
|-------|------|-------------|
| `productId` | string | Reference to product |
| `productName` | string | Product name (denormalized) |
| `quantity` | number | Ordered quantity |
| `price` | number | Unit price in DT |

### Status Enum

- `pending` - Order pending
- `confirmed` - Order confirmed
- `delivered` - Order delivered
- `cancelled` - Order cancelled

---

## 📚 Catalogues Collection (`catalogues`)

**Purpose**: Digital catalogs and marketing materials.

### Core Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | Auto | Firestore document ID |
| `name` | string | ✅ | Catalogue name |
| `description` | string | ✅ | Catalogue description |
| `category` | string | ✅ | Catalogue category |
| `fileUrl` | string | ❌ | File download URL |
| `fileSize` | string | ✅ | File size |
| `downloads` | number | ✅ | Download count |
| `status` | enum | ✅ | Catalogue status |
| `createdAt` | Timestamp | Auto | Creation timestamp |
| `updatedAt` | Timestamp | Auto | Last update timestamp |

### Status Enum

- `active` - Active catalogue
- `inactive` - Inactive catalogue
- `draft` - Draft catalogue

---

## 🎯 Missions Collection (`missions`)

**Purpose**: Field missions assigned to merchandizers.

### Core Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | Auto | Firestore document ID |
| `merchandizerId` | string | ✅ | Reference to merchandizer |
| `merchandizerName` | string | ✅ | Merchandizer name (denormalized) |
| `clientId` | string | ✅ | Reference to client |
| `clientName` | string | ✅ | Client name (denormalized) |
| `clientAddress` | string | ❌ | Client address |
| `title` | string | ✅ | Mission title |
| `description` | string | ✅ | Mission description |
| `missionDate` | Timestamp | ✅ | Mission date |
| `startTime` | string | ✅ | Start time (HH:MM format) |
| `endTime` | string | ❌ | End time (HH:MM format) |
| `status` | enum | ✅ | Mission status |
| `priority` | enum | ✅ | Mission priority |
| `tasks` | string[] | ✅ | List of tasks |
| `notes` | string | ❌ | Mission notes |
| `completedAt` | Timestamp | ❌ | Completion timestamp |
| `completionNotes` | string | ❌ | Completion notes |
| `attachments` | string[] | ❌ | Attachment URLs |
| `location` | object | ❌ | GPS location |
| `createdAt` | Timestamp | Auto | Creation timestamp |
| `updatedAt` | Timestamp | Auto | Last update timestamp |

### Status Enum

- `pending` - Mission pending
- `in_progress` - Mission in progress
- `completed` - Mission completed
- `cancelled` - Mission cancelled

### Priority Enum

- `low` - Low priority
- `medium` - Medium priority
- `high` - High priority
- `urgent` - Urgent priority

### Location Object Structure

| Field | Type | Description |
|-------|------|-------------|
| `latitude` | number | GPS latitude |
| `longitude` | number | GPS longitude |
| `address` | string | Address description |

---

## 🔗 Relationships and Data Flow

### User → Commercial/Merchandizer (FIXED RELATIONSHIPS)
- **Two-way linking**: Users link to employees via `employeeId`, employees link back via `userId`
- **Authentication**: `users` collection handles Firebase Auth and permissions
- **Business Data**: `commercials`/`merchandizers` collections handle work-related information
- **Synchronized Creation**: Both records created simultaneously during account setup
- **Data Consistency**: AccountSyncService maintains synchronization between collections

**Linking Fields:**
- `users.employeeId` → `commercials.employeeId` or `merchandizers.employeeId`
- `commercials.userId` → `users.uid` or `merchandizers.userId` → `users.uid`

### Client → Orders
- One client can have multiple orders
- Orders store `clientId` and denormalized `clientName`

### Commercial → Orders
- One commercial can create multiple orders
- Orders store `commercialId` and denormalized `commercialName`

### Product → Orders
- Products are referenced in order line items
- Order products store `productId` and denormalized `productName`

### Merchandizer → Missions
- One merchandizer can have multiple missions
- Missions store `merchandizerId` and denormalized `merchandizerName`

### Client → Missions
- One client can have multiple missions
- Missions store `clientId` and denormalized `clientName`

---

## 📊 Data Integrity and Validation

### Required Fields Validation
All collections enforce required field validation through TypeScript models and Firebase security rules.

### Data Sanitization
- String fields are sanitized to prevent XSS
- Email validation for email fields
- Phone number format validation
- Numeric field range validation

### Audit Trail
All entities include audit information:
- `createdAt` - Creation timestamp
- `updatedAt` - Last modification timestamp
- Extended models include full audit trails with user tracking

---

## 🔒 Security and Access Control

### Firebase Security Rules
- Users can only access data within their assigned territory
- Commercials can only view their assigned clients
- Merchandizers can only access their assigned missions
- Admin users have full access

### Authentication
- Firebase Auth integration
- Role-based access control
- Session management with configurable timeouts
- Biometric authentication support for mobile apps

---

## 📈 Performance Considerations

### Indexing Strategy
- Composite indexes for common query patterns
- Single field indexes on frequently filtered fields
- Optimized for real-time queries

### Data Denormalization
- Client and commercial names stored in orders for performance
- Product names stored in order line items
- Reduces need for joins in NoSQL environment

### Caching Strategy
- Local caching with offline support
- Real-time listeners for live updates
- Optimistic updates for better UX

---

## 🚀 Future Enhancements

### Planned Collections
- `quotes` - Price quotations
- `reports` - Mission reports with photos
- `territories` - Geographic territory definitions
- `notifications` - System notifications
- `analytics` - Business intelligence data

### Authentication Collections
- `users` - ✅ **Active** - Firebase Auth user profiles and roles

### Schema Evolution
- Backward compatibility maintained
- Migration strategies for schema changes
- Version control for model definitions

---

## � Troubleshooting Account Issues

### Common Problems and Solutions

**Problem**: Commercial/Merchandizer cannot login
```typescript
// Solution: Check if user account exists
const user = await AccountSyncService.findUserByEmployeeId('COMM-123');
if (!user) {
  // Account was not created properly - recreate via modal
}
```

**Problem**: Data inconsistency between collections
```typescript
// Solution: Validate and fix inconsistencies
const validation = await AccountSyncService.validateDataConsistency(userId);
if (!validation.isConsistent) {
  await AccountSyncService.fixDataInconsistencies(userId);
}
```

**Problem**: Employee record exists but no auth account
```typescript
// Solution: Create missing auth account
const commercial = await AccountSyncService.findCommercialByEmployeeId('COMM-123');
if (commercial && !commercial.userId) {
  // Use CommercialModal to recreate with proper auth integration
}
```

### Data Migration for Existing Records

If you have existing commercials/merchandizers without auth accounts:

1. **Backup existing data**
2. **Use the updated modals** to recreate accounts with proper auth integration
3. **Run data validation** using AccountSyncService
4. **Fix any inconsistencies** found

---

## �📝 Notes

1. **Currency**: All monetary values are in Tunisian Dinar (DT)
2. **Timestamps**: All timestamps use Firebase Timestamp type
3. **IDs**: Firestore auto-generates document IDs
4. **Validation**: Client-side validation with TypeScript models
5. **Real-time**: All collections support real-time updates
6. **Offline**: Offline support with local caching
7. **Backup**: Automated daily backups configured
8. **Monitoring**: Performance monitoring and alerting enabled

This documentation is maintained alongside the codebase and updated with each schema change.
