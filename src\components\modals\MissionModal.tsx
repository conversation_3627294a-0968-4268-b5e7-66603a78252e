import React, { useState, useEffect } from "react";
import {
  X,
  Calendar,
  Clock,
  User,
  Building,
  MapPin,
  Plus,
  Trash2,
  Save,
  Loader2,
} from "lucide-react";
import { useApp } from "../../hooks/useApp";
import { Mission, MissionStatus, MissionPriority } from "../../models";

interface MissionModalProps {
  isOpen: boolean;
  onClose: () => void;
  mission?: Mission | null;
  mode: "create" | "edit" | "view";
}

const MissionModal: React.FC<MissionModalProps> = ({
  isOpen,
  onClose,
  mission,
  mode,
}) => {
  const { addMission, updateMission, merchandizers, clients, commercials } =
    useApp();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    titre: "",
    description: "",
    merchandiserId: "",
    clientId: "",
    clientNom: "",
    commercialId: "",
    dateCreation: new Date().toISOString().split("T")[0],
    dateEcheance: "",
    statut: MissionStatus.EN_COURS,
    priorite: MissionPriority.MOYENNE,
    taches: [] as string[],
    notes: "",
    parametres: null as any,
  });

  useEffect(() => {
    if (mission && (mode === "edit" || mode === "view")) {
      setFormData({
        title: mission.title || "",
        description: mission.description || "",
        merchandizerId: mission.merchandizerId || "",
        merchandizerName: mission.merchandizerName || "",
        clientId: mission.clientId || "",
        clientName: mission.clientName || "",
        clientAddress: mission.clientAddress || "",
        missionDate: mission.missionDate
          ? mission.missionDate.toDate().toISOString().split("T")[0]
          : "",
        startTime: mission.startTime || "",
        endTime: mission.endTime || "",
        status: mission.status || "pending",
        priority: mission.priority || "medium",
        tasks: mission.tasks && mission.tasks.length > 0 ? mission.tasks : [""],
        notes: mission.notes || "",
      });
    } else {
      // Reset form for create mode
      setFormData({
        title: "",
        description: "",
        merchandizerId: "",
        merchandizerName: "",
        clientId: "",
        clientName: "",
        clientAddress: "",
        missionDate: "",
        startTime: "",
        endTime: "",
        status: "pending",
        priority: "medium",
        tasks: [""],
        notes: "",
      });
    }
  }, [mission, mode, isOpen]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleMerchandizerChange = (merchandizerId: string) => {
    const selectedMerchandizer = merchandizers.find(
      (m) => m.id === merchandizerId
    );
    setFormData((prev) => ({
      ...prev,
      merchandizerId,
      merchandizerName: selectedMerchandizer?.name || "",
    }));
  };

  const handleClientChange = (clientId: string) => {
    const selectedClient = clients.find((c) => c.id === clientId);
    setFormData((prev) => ({
      ...prev,
      clientId,
      clientName: selectedClient?.nomClient || "",
      clientAddress: selectedClient?.adresse || "",
    }));
  };

  const handleTaskChange = (index: number, value: string) => {
    const newTasks = [...formData.tasks];
    newTasks[index] = value;
    setFormData((prev) => ({ ...prev, tasks: newTasks }));
  };

  const addTask = () => {
    setFormData((prev) => ({ ...prev, tasks: [...prev.tasks, ""] }));
  };

  const removeTask = (index: number) => {
    if (formData.tasks.length > 1) {
      const newTasks = formData.tasks.filter((_, i) => i !== index);
      setFormData((prev) => ({ ...prev, tasks: newTasks }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (mode === "view") return;

    setLoading(true);
    try {
      const missionData = {
        ...formData,
        missionDate: Timestamp.fromDate(new Date(formData.missionDate)),
        tasks: formData.tasks.filter((task) => task.trim() !== ""),
      };

      if (mode === "create") {
        await addMission(missionData);
      } else if (mode === "edit" && mission?.id) {
        await updateMission(mission.id, missionData);
      }

      onClose();
    } catch (error) {
      console.error("Error saving mission:", error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const isReadOnly = mode === "view";

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {mode === "create"
              ? "Nouvelle Mission"
              : mode === "edit"
              ? "Modifier Mission"
              : "Détails Mission"}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Titre de la mission *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange("title", e.target.value)}
                disabled={isReadOnly}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Priorité
              </label>
              <select
                value={formData.priority}
                onChange={(e) => handleInputChange("priority", e.target.value)}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50"
              >
                <option value="low">Faible</option>
                <option value="medium">Moyenne</option>
                <option value="high">Élevée</option>
                <option value="urgent">Urgente</option>
              </select>
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              disabled={isReadOnly}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50"
            />
          </div>

          {/* Assignment */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <User className="inline h-4 w-4 mr-1" />
                Merchandizer *
              </label>
              <select
                value={formData.merchandizerId}
                onChange={(e) => handleMerchandizerChange(e.target.value)}
                disabled={isReadOnly}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50"
              >
                <option value="">Sélectionner un merchandizer</option>
                {merchandizers.map((merchandizer) => (
                  <option key={merchandizer.id} value={merchandizer.id}>
                    {merchandizer.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Building className="inline h-4 w-4 mr-1" />
                Client *
              </label>
              <select
                value={formData.clientId}
                onChange={(e) => handleClientChange(e.target.value)}
                disabled={isReadOnly}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50"
              >
                <option value="">Sélectionner un client</option>
                {clients.map((client) => (
                  <option key={client.id} value={client.id}>
                    {client.nomClient}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Date and Time */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Date de mission *
              </label>
              <input
                type="date"
                value={formData.missionDate}
                onChange={(e) =>
                  handleInputChange("missionDate", e.target.value)
                }
                disabled={isReadOnly}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Clock className="inline h-4 w-4 mr-1" />
                Heure de début *
              </label>
              <input
                type="time"
                value={formData.startTime}
                onChange={(e) => handleInputChange("startTime", e.target.value)}
                disabled={isReadOnly}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Clock className="inline h-4 w-4 mr-1" />
                Heure de fin
              </label>
              <input
                type="time"
                value={formData.endTime}
                onChange={(e) => handleInputChange("endTime", e.target.value)}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50"
              />
            </div>
          </div>

          {/* Status (only for edit mode) */}
          {mode === "edit" && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Statut
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleInputChange("status", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="pending">En attente</option>
                <option value="in_progress">En cours</option>
                <option value="completed">Terminée</option>
                <option value="cancelled">Annulée</option>
              </select>
            </div>
          )}

          {/* Tasks */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tâches à accomplir
            </label>
            <div className="space-y-2">
              {formData.tasks.map((task, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={task}
                    onChange={(e) => handleTaskChange(index, e.target.value)}
                    disabled={isReadOnly}
                    placeholder={`Tâche ${index + 1}`}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50"
                  />
                  {!isReadOnly && formData.tasks.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeTask(index)}
                      className="text-red-600 hover:text-red-800 transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  )}
                </div>
              ))}
              {!isReadOnly && (
                <button
                  type="button"
                  onClick={addTask}
                  className="flex items-center space-x-2 text-blue-600 hover:text-blue-800 transition-colors"
                >
                  <Plus className="h-4 w-4" />
                  <span>Ajouter une tâche</span>
                </button>
              )}
            </div>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              disabled={isReadOnly}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50"
            />
          </div>

          {/* Client Address Display */}
          {formData.clientAddress && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <MapPin className="inline h-4 w-4 mr-1" />
                Adresse du client
              </label>
              <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-700">
                {formData.clientAddress}
              </div>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              {mode === "view" ? "Fermer" : "Annuler"}
            </button>
            {!isReadOnly && (
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {loading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                )}
                <span>
                  {mode === "create" ? "Créer Mission" : "Sauvegarder"}
                </span>
              </button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
};

export default MissionModal;
