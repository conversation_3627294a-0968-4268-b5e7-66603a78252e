import React, { useState } from "react";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  FileText,
  Calendar,
} from "lucide-react";
import { DevisModal } from "../components/modals";
import { useApp } from "../hooks/useApp";
import { Devis, formatDevisDate } from "../models";

const DevisManagement: React.FC = () => {
  const { devis, deleteDevis } = useApp();
  
  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    mode: 'create' | 'edit' | 'view';
    devis?: Devis | null;
  }>({ isOpen: false, mode: 'create' });

  const [searchTerm, setSearchTerm] = useState("");

  const filteredDevis = devis.filter(
    (d) => {
      if (!searchTerm) return true;
      
      const searchTermLower = searchTerm.toLowerCase();
      const numero = d.numero?.toLowerCase() || '';
      const notes = d.notes?.toLowerCase() || '';
      
      return numero.includes(searchTermLower) ||
             notes.includes(searchTermLower);
    }
  );

  const handleEdit = (devis: Devis) => {
    setModalState({
      isOpen: true,
      mode: 'edit',
      devis
    });
  };

  const handleView = (devis: Devis) => {
    setModalState({
      isOpen: true,
      mode: 'view',
      devis
    });
  };

  const handleDelete = (id: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce devis ?')) {
      deleteDevis(id);
    }
  };

  const handleAdd = () => {
    setModalState({
      isOpen: true,
      mode: 'create',
      devis: null
    });
  };

  const closeModal = () => {
    setModalState({ isOpen: false, mode: 'create', devis: null });
  };

  const getStatusColor = (statut: string) => {
    switch (statut) {
      case 'brouillon':
        return 'bg-gray-100 text-gray-800';
      case 'envoye':
        return 'bg-blue-100 text-blue-800';
      case 'accepte':
        return 'bg-green-100 text-green-800';
      case 'refuse':
        return 'bg-red-100 text-red-800';
      case 'expire':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (statut: string) => {
    switch (statut) {
      case 'brouillon':
        return 'Brouillon';
      case 'envoye':
        return 'Envoyé';
      case 'accepte':
        return 'Accepté';
      case 'refuse':
        return 'Refusé';
      case 'expire':
        return 'Expiré';
      default:
        return statut;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gestion des Devis</h1>
          <p className="mt-1 text-sm text-gray-500">
            Gérez vos devis et propositions commerciales
          </p>
        </div>
        <button
          onClick={handleAdd}
          className="mt-4 sm:mt-0 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Nouveau Devis</span>
        </button>
      </div>

      {/* Search */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Rechercher par numéro, notes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Devis Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredDevis.length === 0 ? (
          <div className="col-span-full flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm ? "Aucun devis trouvé" : "Aucun devis"}
            </h3>
            <p className="text-gray-500 mb-4">
              {searchTerm
                ? "Aucun devis ne correspond à vos critères de recherche."
                : "Commencez par créer votre premier devis."}
            </p>
            {!searchTerm && (
              <button
                onClick={handleAdd}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <Plus className="h-4 w-4" />
                <span>Nouveau Devis</span>
              </button>
            )}
          </div>
        ) : (
          filteredDevis.map((devisItem) => (
            <div
              key={devisItem.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {devisItem.numero}
                    </h3>
                    <p className="text-sm text-gray-600">
                      Client ID: {devisItem.clientId}
                    </p>
                  </div>
                </div>
                <span
                  className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(devisItem.statut)}`}
                >
                  {getStatusLabel(devisItem.statut)}
                </span>
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-500 flex items-center">
                    <Calendar className="h-3 w-3 mr-1" />
                    Créé le:
                  </span>
                  <span className="text-gray-900">
                    {formatDevisDate(devisItem.dateCreation)}
                  </span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-500 flex items-center">
                    <Calendar className="h-3 w-3 mr-1" />
                    Expire le:
                  </span>
                  <span className="text-gray-900">
                    {formatDevisDate(devisItem.dateExpiration)}
                  </span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-500">Articles:</span>
                  <span className="text-gray-900">{devisItem.items.length}</span>
                </div>
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={() => handleView(devisItem)}
                  className="bg-gray-50 text-gray-600 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors"
                  title="Voir"
                >
                  <Eye className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleEdit(devisItem)}
                  className="bg-blue-50 text-blue-600 px-3 py-2 rounded-lg hover:bg-blue-100 transition-colors"
                  title="Modifier"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDelete(devisItem.id)}
                  className="bg-red-50 text-red-600 px-3 py-2 rounded-lg hover:bg-red-100 transition-colors"
                  title="Supprimer"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))
        )}
      </div>

      <DevisModal
        isOpen={modalState.isOpen}
        onClose={closeModal}
        devis={modalState.devis}
        mode={modalState.mode}
      />
    </div>
  );
};

export default DevisManagement;
