import { Timestamp } from 'firebase/firestore';
import {
  BaseEntity,
  ValidationResult,
  ValidationError,
  CreateEntity,
  UpdateEntity,
  sanitizeString,
  VALIDATION_LIMITS
} from './common';

// ============================================================================
// PRODUCT ENUMS
// ============================================================================

/**
 * Product type enumeration
 */
export enum ProductType {
  PHYSICAL = 'physical',
  DIGITAL = 'digital',
  SERVICE = 'service'
}

/**
 * Product availability enumeration
 */
export enum ProductAvailability {
  IN_STOCK = 'in_stock',
  OUT_OF_STOCK = 'out_of_stock',
  DISCONTINUED = 'discontinued'
}

/**
 * Product condition enumeration
 */
export enum ProductCondition {
  NEW = 'new',
  USED = 'used',
  REFURBISHED = 'refurbished'
}

// ============================================================================
// PRODUCT INTERFACES
// ============================================================================

/**
 * Main Product interface - simplified to match database schema (produits collection)
 */
export interface Product extends BaseEntity {
  // Basic Information
  nom: string; // Nom du produit
  description: string; // Description du produit
  code: string; // Code du produit
  categorie: string; // Catégorie du produit

  // Pricing and Stock
  prix: number; // Prix du produit
  stock: number; // Stock disponible

  // Status and Media
  actif: boolean; // Statut actif/inactif
  imageUrl: string | null; // URL de l'image (peut être null)

  // Dates and Identification
  dateCreation: string; // Date de création (format ISO string)
  id: string; // ID du produit
}
// ============================================================================
// PRODUCT FORM INTERFACES
// ============================================================================

/**
 * Product form data interface (for forms)
 */
export interface ProductFormData {
  nom: string;
  description: string;
  code: string;
  categorie: string;
  prix: number;
  stock: number;
  actif: boolean;
  imageUrl: string | null;
  dateCreation: string;
}

/**
 * Product search/filter interface
 */
export interface ProductSearchFilters {
  nom?: string;
  description?: string;
  code?: string;
  categorie?: string;
  actif?: boolean;
  prixMin?: number;
  prixMax?: number;
  stockMin?: number;
  stockMax?: number;
  dateCreationAfter?: string;
  dateCreationBefore?: string;
}

// ============================================================================
// TYPE ALIASES
// ============================================================================

export type CreateProductData = CreateEntity<Product>;
export type UpdateProductData = UpdateEntity<Product>;
export type ProductWithId = Product & Required<Pick<Product, 'id'>>;

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate product form data
 */
export const validateProductData = (data: Partial<ProductFormData>): ValidationResult => {
  const errors: ValidationError[] = [];

  // Required fields validation
  if (!data.nom || data.nom.trim().length === 0) {
    errors.push({
      field: 'nom',
      message: 'Le nom du produit est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.description || data.description.trim().length === 0) {
    errors.push({
      field: 'description',
      message: 'La description est requise',
      code: 'REQUIRED'
    });
  }

  if (!data.code || data.code.trim().length === 0) {
    errors.push({
      field: 'code',
      message: 'Le code produit est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.categorie || data.categorie.trim().length === 0) {
    errors.push({
      field: 'categorie',
      message: 'La catégorie est requise',
      code: 'REQUIRED'
    });
  }

  if (!data.dateCreation || data.dateCreation.trim().length === 0) {
    errors.push({
      field: 'dateCreation',
      message: 'La date de création est requise',
      code: 'REQUIRED'
    });
  }

  // Numeric validations
  if (data.prix !== undefined && data.prix < 0) {
    errors.push({
      field: 'prix',
      message: 'Le prix ne peut pas être négatif',
      code: 'INVALID_VALUE'
    });
  }

  if (data.stock !== undefined && data.stock < 0) {
    errors.push({
      field: 'stock',
      message: 'Le stock ne peut pas être négatif',
      code: 'INVALID_VALUE'
    });
  }

  // Length validations
  if (data.nom && data.nom.length > VALIDATION_LIMITS.NAME_MAX_LENGTH) {
    errors.push({
      field: 'nom',
      message: `Le nom ne peut pas dépasser ${VALIDATION_LIMITS.NAME_MAX_LENGTH} caractères`,
      code: 'INVALID_LENGTH'
    });
  }

  if (data.description && data.description.length > VALIDATION_LIMITS.DESCRIPTION_MAX_LENGTH) {
    errors.push({
      field: 'description',
      message: `La description ne peut pas dépasser ${VALIDATION_LIMITS.DESCRIPTION_MAX_LENGTH} caractères`,
      code: 'INVALID_LENGTH'
    });
  }

  if (data.code && data.code.length > 50) {
    errors.push({
      field: 'code',
      message: 'Le code produit ne peut pas dépasser 50 caractères',
      code: 'INVALID_LENGTH'
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};
/**
 * Sanitize product data before saving
 */
export const sanitizeProductData = (data: ProductFormData): ProductFormData => {
  return {
    ...data,
    nom: sanitizeString(data.nom),
    description: sanitizeString(data.description),
    code: sanitizeString(data.code).toUpperCase(),
    categorie: sanitizeString(data.categorie),
    dateCreation: data.dateCreation.trim()
  };
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get product display name
 */
export const getProductDisplayName = (product: Product): string => {
  return product.nom;
};

/**
 * Check if product is active
 */
export const isProductActive = (product: Product): boolean => {
  return product.actif;
};

/**
 * Check if product is low stock
 */
export const isLowStock = (product: Product, threshold: number = 10): boolean => {
  return product.stock <= threshold;
};

/**
 * Check if product is out of stock
 */
export const isOutOfStock = (product: Product): boolean => {
  return product.stock <= 0;
};

/**
 * Format product price
 */
export const formatProductPrice = (price: number, currency: string = 'TND'): string => {
  return `${price.toFixed(2)} ${currency}`;
};

/**
 * Generate product code
 */
export const generateProductCode = (prefix: string = 'PRD'): string => {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}-${timestamp}-${random}`;
};

/**
 * Format product date
 */
export const formatProductDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('fr-FR');
};
