import { 
  doc, 
  getDoc, 
  updateDoc, 
  query, 
  where, 
  getDocs, 
  collection,
  Timestamp 
} from 'firebase/firestore';
import { db } from './config';
import { AuthService } from './auth';

/**
 * Account Synchronization Service
 * Maintains data consistency between users, commercials, and merchandizers collections
 */
export class AccountSyncService {
  
  /**
   * Sync user profile with commercial record
   */
  static async syncUserWithCommercial(userId: string, commercialId: string): Promise<void> {
    try {
      // Get user profile
      const userProfile = await AuthService.getUserProfile(userId);
      if (!userProfile) {
        throw new Error('User profile not found');
      }

      // Get commercial record
      const commercialDoc = await getDoc(doc(db, 'commercials', commercialId));
      if (!commercialDoc.exists()) {
        throw new Error('Commercial record not found');
      }

      const commercial = commercialDoc.data();

      // Update user profile with commercial info
      await updateDoc(doc(db, 'users', userId), {
        employeeId: commercial.employeeId,
        department: commercial.department,
        territory: commercial.territory,
        updatedAt: Timestamp.now()
      });

      // Update commercial record with user link
      await updateDoc(doc(db, 'commercials', commercialId), {
        userId: userId,
        updatedAt: Timestamp.now()
      });

    } catch (error) {
      console.error('Error syncing user with commercial:', error);
      throw error;
    }
  }

  /**
   * Sync user profile with merchandizer record
   */
  static async syncUserWithMerchandizer(userId: string, merchandizerId: string): Promise<void> {
    try {
      // Get user profile
      const userProfile = await AuthService.getUserProfile(userId);
      if (!userProfile) {
        throw new Error('User profile not found');
      }

      // Get merchandizer record
      const merchandizerDoc = await getDoc(doc(db, 'merchandizers', merchandizerId));
      if (!merchandizerDoc.exists()) {
        throw new Error('Merchandizer record not found');
      }

      const merchandizer = merchandizerDoc.data();

      // Update user profile with merchandizer info
      await updateDoc(doc(db, 'users', userId), {
        employeeId: merchandizer.employeeId,
        department: merchandizer.department,
        territory: merchandizer.territory,
        updatedAt: Timestamp.now()
      });

      // Update merchandizer record with user link
      await updateDoc(doc(db, 'merchandizers', merchandizerId), {
        userId: userId,
        updatedAt: Timestamp.now()
      });

    } catch (error) {
      console.error('Error syncing user with merchandizer:', error);
      throw error;
    }
  }

  /**
   * Find commercial by employee ID
   */
  static async findCommercialByEmployeeId(employeeId: string): Promise<any | null> {
    try {
      const q = query(
        collection(db, 'commercials'),
        where('employeeId', '==', employeeId)
      );
      
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return null;
      }

      const doc = querySnapshot.docs[0];
      return {
        id: doc.id,
        ...doc.data()
      };
    } catch (error) {
      console.error('Error finding commercial by employee ID:', error);
      return null;
    }
  }

  /**
   * Find merchandizer by employee ID
   */
  static async findMerchandizerByEmployeeId(employeeId: string): Promise<any | null> {
    try {
      const q = query(
        collection(db, 'merchandizers'),
        where('employeeId', '==', employeeId)
      );
      
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return null;
      }

      const doc = querySnapshot.docs[0];
      return {
        id: doc.id,
        ...doc.data()
      };
    } catch (error) {
      console.error('Error finding merchandizer by employee ID:', error);
      return null;
    }
  }

  /**
   * Find user by employee ID
   */
  static async findUserByEmployeeId(employeeId: string): Promise<any | null> {
    try {
      const q = query(
        collection(db, 'users'),
        where('employeeId', '==', employeeId)
      );
      
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return null;
      }

      const doc = querySnapshot.docs[0];
      return {
        uid: doc.id,
        ...doc.data()
      };
    } catch (error) {
      console.error('Error finding user by employee ID:', error);
      return null;
    }
  }

  /**
   * Get complete employee profile (user + commercial/merchandizer data)
   */
  static async getCompleteEmployeeProfile(userId: string): Promise<any | null> {
    try {
      // Get user profile
      const userProfile = await AuthService.getUserProfile(userId);
      if (!userProfile) {
        return null;
      }

      let employeeData = null;

      // Get employee business data based on role
      if (userProfile.role === 'commercial') {
        employeeData = await this.findCommercialByEmployeeId(userProfile.employeeId);
      } else if (userProfile.role === 'merchandiser') {
        employeeData = await this.findMerchandizerByEmployeeId(userProfile.employeeId);
      }

      return {
        user: userProfile,
        employee: employeeData,
        role: userProfile.role
      };
    } catch (error) {
      console.error('Error getting complete employee profile:', error);
      return null;
    }
  }

  /**
   * Update employee data across all collections
   */
  static async updateEmployeeData(
    userId: string, 
    updates: {
      name?: string;
      email?: string;
      phone?: string;
      territory?: string;
      status?: string;
    }
  ): Promise<void> {
    try {
      const profile = await this.getCompleteEmployeeProfile(userId);
      if (!profile) {
        throw new Error('Employee profile not found');
      }

      // Update user profile
      const userUpdates: any = {
        updatedAt: Timestamp.now()
      };
      
      if (updates.name) userUpdates.displayName = updates.name;
      if (updates.territory) userUpdates.territory = updates.territory;

      await updateDoc(doc(db, 'users', userId), userUpdates);

      // Update employee business record
      if (profile.employee) {
        const employeeUpdates: any = {
          updatedAt: Timestamp.now()
        };

        if (updates.name) {
          employeeUpdates.name = updates.name;
          employeeUpdates.firstName = updates.name.split(' ')[0] || updates.name;
          employeeUpdates.lastName = updates.name.split(' ').slice(1).join(' ') || '';
        }
        if (updates.email) employeeUpdates.email = updates.email;
        if (updates.phone) {
          employeeUpdates.phone = updates.phone;
          employeeUpdates.mobile = updates.phone;
        }
        if (updates.territory) employeeUpdates.territory = updates.territory;
        if (updates.status) employeeUpdates.status = updates.status;

        const collectionName = profile.role === 'commercial' ? 'commercials' : 'merchandizers';
        await updateDoc(doc(db, collectionName, profile.employee.id), employeeUpdates);
      }

    } catch (error) {
      console.error('Error updating employee data:', error);
      throw error;
    }
  }

  /**
   * Validate data consistency between collections
   */
  static async validateDataConsistency(userId: string): Promise<{
    isConsistent: boolean;
    issues: string[];
  }> {
    try {
      const profile = await this.getCompleteEmployeeProfile(userId);
      if (!profile) {
        return {
          isConsistent: false,
          issues: ['User profile not found']
        };
      }

      const issues: string[] = [];

      if (!profile.employee) {
        issues.push(`No ${profile.role} record found for employee ID: ${profile.user.employeeId}`);
      } else {
        // Check email consistency
        if (profile.user.email !== profile.employee.email) {
          issues.push('Email mismatch between user and employee records');
        }

        // Check territory consistency
        if (profile.user.territory !== profile.employee.territory) {
          issues.push('Territory mismatch between user and employee records');
        }

        // Check employee ID consistency
        if (profile.user.employeeId !== profile.employee.employeeId) {
          issues.push('Employee ID mismatch between user and employee records');
        }
      }

      return {
        isConsistent: issues.length === 0,
        issues
      };

    } catch (error) {
      console.error('Error validating data consistency:', error);
      return {
        isConsistent: false,
        issues: ['Error during validation: ' + error.message]
      };
    }
  }

  /**
   * Fix data inconsistencies
   */
  static async fixDataInconsistencies(userId: string): Promise<void> {
    try {
      const validation = await this.validateDataConsistency(userId);
      
      if (validation.isConsistent) {
        return; // No issues to fix
      }

      const profile = await this.getCompleteEmployeeProfile(userId);
      if (!profile || !profile.employee) {
        throw new Error('Cannot fix inconsistencies: missing profile data');
      }

      // Use user data as the source of truth and update employee record
      await this.updateEmployeeData(userId, {
        name: profile.user.displayName,
        email: profile.user.email,
        territory: profile.user.territory
      });

    } catch (error) {
      console.error('Error fixing data inconsistencies:', error);
      throw error;
    }
  }
}
