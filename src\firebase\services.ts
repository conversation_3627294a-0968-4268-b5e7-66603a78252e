import {
  collection,
  addDoc,
  getDocs,
  doc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  limit,
  onSnapshot,
  Timestamp,
  where
} from "firebase/firestore";
import { db } from "./config";

// Import proper TypeScript models
import {
  Client,
  Commercial,
  Product,
  Order,
  Merchandizer,
  Catalogue,
  Mission,
  Devis,
  CreateEntity,
  UpdateEntity
} from "../models";

// Mission interface (not in models yet)
export interface Mission {
  id?: string;
  merchandizerId: string;
  merchandizerName: string;
  clientId: string;
  clientName: string;
  clientAddress?: string;
  title: string;
  description: string;
  missionDate: Timestamp;
  startTime: string; // Format: "HH:MM"
  endTime?: string; // Format: "HH:MM"
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  tasks: string[]; // List of specific tasks to accomplish
  notes?: string;
  completedAt?: Timestamp;
  completionNotes?: string;
  attachments?: string[]; // URLs to photos or documents
  location?: {
    latitude: number;
    longitude: number;
    address: string;
  };
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}





// Firebase service class
export class FirebaseService {
  // Helper function to remove undefined values from objects
  private static cleanData(data: unknown): unknown {
    if (data === null || data === undefined) {
      return null;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.cleanData(item));
    }

    if (typeof data === 'object' && data !== null && data.constructor === Object) {
      const cleaned: Record<string, unknown> = {};
      for (const [key, value] of Object.entries(data)) {
        if (value !== undefined) {
          cleaned[key] = this.cleanData(value);
        }
      }
      return cleaned;
    }

    return data;
  }

  // Generic CRUD operations
  static async addDocument(collectionName: string, data: Record<string, unknown>) {
    try {
      const cleanedData = this.cleanData({
        ...data,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }) as Record<string, unknown>;

      const docRef = await addDoc(collection(db, collectionName), cleanedData);
      return docRef.id;
    } catch (error) {
      console.error("Error adding document: ", error);
      throw error;
    }
  }

  static async getDocuments(collectionName: string, orderByField?: string, limitCount?: number) {
    try {
      let q = query(collection(db, collectionName));
      
      if (orderByField) {
        q = query(q, orderBy(orderByField, 'desc'));
      }
      
      if (limitCount) {
        q = query(q, limit(limitCount));
      }

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error("Error getting documents: ", error);
      throw error;
    }
  }

  static async updateDocument(collectionName: string, docId: string, data: Record<string, unknown>) {
    try {
      const cleanedData = this.cleanData({
        ...data,
        updatedAt: Timestamp.now()
      }) as Record<string, unknown>;

      const docRef = doc(db, collectionName, docId);
      await updateDoc(docRef, cleanedData);
    } catch (error) {
      console.error("Error updating document: ", error);
      throw error;
    }
  }

  static async deleteDocument(collectionName: string, docId: string) {
    try {
      await deleteDoc(doc(db, collectionName, docId));
    } catch (error) {
      console.error("Error deleting document: ", error);
      throw error;
    }
  }

  // Real-time listeners
  static subscribeToCollection(collectionName: string, callback: (data: any[]) => void) {
    const q = query(collection(db, collectionName), orderBy('createdAt', 'desc'));
    
    return onSnapshot(q, (querySnapshot) => {
      const docs = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      callback(docs);
    });
  }

  // Specific methods for each entity
  
  // Clients
  static async addClient(client: CreateEntity<Client>) {
    return this.addDocument('clients', client as Record<string, unknown>);
  }

  static async getClients(): Promise<Client[]> {
    return this.getDocuments('clients', 'createdAt') as Promise<Client[]>;
  }

  static async updateClient(clientId: string, client: Partial<Client>) {
    return this.updateDocument('clients', clientId, client as Record<string, unknown>);
  }

  static async deleteClient(clientId: string) {
    return this.deleteDocument('clients', clientId);
  }

  // Commercials
  static async addCommercial(commercial: CreateEntity<Commercial>) {
    return this.addDocument('commercials', commercial as Record<string, unknown>);
  }

  static async getCommercials(): Promise<Commercial[]> {
    return this.getDocuments('commercials', 'createdAt') as Promise<Commercial[]>;
  }

  static async updateCommercial(commercialId: string, commercial: Partial<Commercial>) {
    return this.updateDocument('commercials', commercialId, commercial as Record<string, unknown>);
  }

  static async deleteCommercial(commercialId: string) {
    return this.deleteDocument('commercials', commercialId);
  }

  // Products (produits collection)
  static async addProduct(product: CreateEntity<Product>) {
    return this.addDocument('produits', product as Record<string, unknown>);
  }

  static async getProducts(): Promise<Product[]> {
    return this.getDocuments('produits', 'dateCreation') as Promise<Product[]>;
  }

  static async updateProduct(productId: string, product: Partial<Product>) {
    return this.updateDocument('produits', productId, product as Record<string, unknown>);
  }

  static async deleteProduct(productId: string) {
    return this.deleteDocument('produits', productId);
  }

  // Orders (commandes collection)
  static async addOrder(order: CreateEntity<Order>) {
    return this.addDocument('commandes', order as Record<string, unknown>);
  }

  static async getOrders(): Promise<Order[]> {
    return this.getDocuments('commandes', 'dateCommande') as Promise<Order[]>;
  }

  static async updateOrder(orderId: string, order: Partial<Order>) {
    return this.updateDocument('commandes', orderId, order as Record<string, unknown>);
  }

  static async deleteOrder(orderId: string) {
    return this.deleteDocument('commandes', orderId);
  }

  // Devis
  static async addDevis(devis: CreateEntity<Devis>) {
    return this.addDocument('devis', devis as Record<string, unknown>);
  }

  static async getDevis(): Promise<Devis[]> {
    return this.getDocuments('devis', 'dateCreation') as Promise<Devis[]>;
  }

  static async updateDevis(devisId: string, devis: Partial<Devis>) {
    return this.updateDocument('devis', devisId, devis as Record<string, unknown>);
  }

  static async deleteDevis(devisId: string) {
    return this.deleteDocument('devis', devisId);
  }

  // Merchandizers
  static async addMerchandizer(merchandizer: CreateEntity<Merchandizer>) {
    return this.addDocument('merchandizers', merchandizer as Record<string, unknown>);
  }

  static async getMerchandizers(): Promise<Merchandizer[]> {
    return this.getDocuments('merchandizers', 'createdAt') as Promise<Merchandizer[]>;
  }

  static async updateMerchandizer(merchandizerId: string, merchandizer: Partial<Merchandizer>) {
    return this.updateDocument('merchandizers', merchandizerId, merchandizer as Record<string, unknown>);
  }

  static async deleteMerchandizer(merchandizerId: string) {
    return this.deleteDocument('merchandizers', merchandizerId);
  }

  // Catalogues
  static async addCatalogue(catalogue: CreateEntity<Catalogue>) {
    return this.addDocument('catalogues', catalogue as Record<string, unknown>);
  }

  static async getCatalogues(): Promise<Catalogue[]> {
    return this.getDocuments('catalogues', 'createdAt') as Promise<Catalogue[]>;
  }

  static async updateCatalogue(catalogueId: string, catalogue: Partial<Catalogue>) {
    return this.updateDocument('catalogues', catalogueId, catalogue as Record<string, unknown>);
  }

  static async deleteCatalogue(catalogueId: string) {
    return this.deleteDocument('catalogues', catalogueId);
  }

  // Missions
  static async addMission(mission: CreateEntity<Mission>) {
    return this.addDocument('missions', mission as Record<string, unknown>);
  }

  static async getMissions(): Promise<Mission[]> {
    return this.getDocuments('missions', 'dateCreation') as Promise<Mission[]>;
  }

  static async getMissionsByMerchandizer(merchandizerId: string): Promise<Mission[]> {
    try {
      const q = query(
        collection(db, 'missions'),
        where('merchandiserId', '==', merchandizerId),
        orderBy('dateCreation', 'desc')
      );
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Mission[];
    } catch (error) {
      console.error("Error getting missions by merchandizer: ", error);
      throw error;
    }
  }

  static async getMissionsByDate(date: Date): Promise<Mission[]> {
    try {
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      const q = query(
        collection(db, 'missions'),
        where('dateEcheance', '>=', startOfDay.toISOString()),
        where('dateEcheance', '<=', endOfDay.toISOString()),
        orderBy('dateEcheance', 'asc')
      );
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Mission[];
    } catch (error) {
      console.error("Error getting missions by date: ", error);
      throw error;
    }
  }

  static async updateMission(missionId: string, mission: Partial<Mission>) {
    return this.updateDocument('missions', missionId, mission as Record<string, unknown>);
  }

  static async deleteMission(missionId: string) {
    return this.deleteDocument('missions', missionId);
  }

  // Dashboard statistics
  static async getDashboardStats() {
    try {
      const [clients, orders, products] = await Promise.all([
        this.getClients(),
        this.getOrders(),
        this.getProducts()
      ]);

      const activeClients = clients.filter(client => client.status === 'active');
      const totalSales = orders.reduce((sum, order) => sum + order.totalAmount, 0);

      // Calculate growth percentages (comparing last 30 days vs previous 30 days)
      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

      // Recent period (last 30 days)
      const recentOrdersForGrowth = orders.filter(order => {
        const orderDate = order.createdAt?.toDate() || new Date(0);
        return orderDate >= thirtyDaysAgo;
      });

      // Previous period (30-60 days ago)
      const previousOrders = orders.filter(order => {
        const orderDate = order.createdAt?.toDate() || new Date(0);
        return orderDate >= sixtyDaysAgo && orderDate < thirtyDaysAgo;
      });

      // Recent clients (last 30 days)
      const recentClients = clients.filter(client => {
        const clientDate = client.createdAt?.toDate() || new Date(0);
        return clientDate >= thirtyDaysAgo;
      });

      // Previous clients (30-60 days ago)
      const previousClients = clients.filter(client => {
        const clientDate = client.createdAt?.toDate() || new Date(0);
        return clientDate >= sixtyDaysAgo && clientDate < thirtyDaysAgo;
      });

      // Recent products (last 30 days)
      const recentProducts = products.filter(product => {
        const productDate = product.createdAt?.toDate() || new Date(0);
        return productDate >= thirtyDaysAgo;
      });

      // Previous products (30-60 days ago)
      const previousProducts = products.filter(product => {
        const productDate = product.createdAt?.toDate() || new Date(0);
        return productDate >= sixtyDaysAgo && productDate < thirtyDaysAgo;
      });

      // Calculate growth percentages
      const calculateGrowth = (current: number, previous: number) => {
        if (previous === 0) return current > 0 ? 100 : 0;
        return ((current - previous) / previous) * 100;
      };

      const recentSales = recentOrdersForGrowth.reduce((sum: number, order: Order) => sum + order.totalAmount, 0);
      const previousSales = previousOrders.reduce((sum: number, order: Order) => sum + order.totalAmount, 0);

      // Top clients by order amount
      const clientOrderTotals = new Map();
      orders.forEach(order => {
        const current = clientOrderTotals.get(order.clientId) || {
          name: order.clientName,
          amount: 0,
          orders: 0
        };
        current.amount += order.totalAmount;
        current.orders += 1;
        clientOrderTotals.set(order.clientId, current);
      });

      const topClients = Array.from(clientOrderTotals.entries())
        .map(([id, data]) => ({ id, ...data }))
        .sort((a, b) => b.amount - a.amount)
        .slice(0, 5);

      return {
        totalSales,
        activeClients: activeClients.length,
        totalProducts: products.length,
        totalOrders: orders.length,
        topClients,
        recentOrders: orders.slice(0, 5),
        salesGrowth: calculateGrowth(recentSales, previousSales),
        clientsGrowth: calculateGrowth(recentClients.length, previousClients.length),
        productsGrowth: calculateGrowth(recentProducts.length, previousProducts.length),
        ordersGrowth: calculateGrowth(recentOrdersForGrowth.length, previousOrders.length),
      };
    } catch (error) {
      console.error("Error getting dashboard stats: ", error);
      throw error;
    }
  }

  // Network error handling and retry logic
  static async withRetry<T>(operation: () => Promise<T>, maxRetries = 3): Promise<T> {
    let lastError: unknown;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error: unknown) {
        lastError = error;
        
        // Check if it's a network error
        const errorCode = error instanceof Error && 'code' in error ? (error as { code: string }).code : '';
        const errorMessage = error instanceof Error ? error.message : '';
        
        if (errorCode === 'unavailable' || 
            errorCode === 'deadline-exceeded' || 
            errorMessage.includes('net::ERR_QUIC_PROTOCOL_ERROR')) {
          
          console.warn(`Network error on attempt ${attempt}/${maxRetries}:`, error);
          
          if (attempt < maxRetries) {
            // Wait before retrying (exponential backoff)
            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
        }
        
        // If it's not a network error or we've exhausted retries, throw immediately
        throw error;
      }
    }
    
    throw lastError;
  }

  // Enhanced methods with retry logic
  static async getDocumentsWithRetry(collectionName: string, orderByField?: string, limitCount?: number) {
    return this.withRetry(() => this.getDocuments(collectionName, orderByField, limitCount));
  }

  static async getDashboardStatsWithRetry() {
    return this.withRetry(() => this.getDashboardStats());
  }

  // Add sample data for testing (can be removed in production)
  static async addSampleData() {
    try {
      // Add sample clients - using simplified data that matches current usage
      const sampleClients = [
        {
          codeClient: "CLI001",
          nomClient: "Société ABC",
          email: "<EMAIL>",
          tel: "71234567",
          fax: "71234568",
          portable: "98765432",
          company: "ABC SARL",
          adresse: "123 Avenue Habib Bourguiba, Tunis",
          matriculeFiscale: "1234567A",
          modeReglement: "cheque" as const,
          status: "active" as const
        },
        {
          codeClient: "CLI002",
          nomClient: "Entreprise XYZ",
          email: "<EMAIL>",
          tel: "71987654",
          fax: "71987655",
          portable: "23456789",
          company: "XYZ Ltd",
          adresse: "456 Rue de la République, Sfax",
          matriculeFiscale: "9876543B",
          modeReglement: "virement" as const,
          status: "active" as const
        }
      ];

      // Add sample orders
      const sampleOrders = [
        {
          orderNumber: "CMD001",
          clientId: "CLI001",
          clientName: "Société ABC",
          totalAmount: 1500.50,
          status: "completed",
          items: [
            { productId: "PROD001", productName: "Produit A", quantity: 10, unitPrice: 150.05 }
          ]
        },
        {
          orderNumber: "CMD002",
          clientId: "CLI002",
          clientName: "Entreprise XYZ",
          totalAmount: 2300.75,
          status: "completed",
          items: [
            { productId: "PROD002", productName: "Produit B", quantity: 15, unitPrice: 153.38 }
          ]
        }
      ];

      // Add clients
      for (const client of sampleClients) {
        await this.addClient(client);
      }

      // Add orders
      for (const order of sampleOrders) {
        await this.addOrder(order);
      }

      console.log("Sample data added successfully");
    } catch (error) {
      console.error("Error adding sample data:", error);
      throw error;
    }
  }
}

export default FirebaseService;
