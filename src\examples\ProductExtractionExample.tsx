import React, { useState } from "react";
import { FileText, Zap, Download, Eye } from "lucide-react";
import { CatalogueModal, ProductExtractionModal } from "../components/modals";
import { ExtractedProduct } from "../services/pdfExtractor";

const ProductExtractionExample: React.FC = () => {
  const [showCatalogueModal, setShowCatalogueModal] = useState(false);
  const [showExtractionModal, setShowExtractionModal] = useState(false);
  const [extractedProducts, setExtractedProducts] = useState<
    ExtractedProduct[]
  >([]);
  const [extractionResults, setExtractionResults] = useState<string | null>(
    null
  );

  const handleProductsExtracted = (products: ExtractedProduct[]) => {
    setExtractedProducts(products);
    setShowExtractionModal(false);

    // Générer un résumé des résultats
    const summary = `
✅ Extraction terminée avec succès !

📊 Résultats :
• ${products.length} produits extraits
• ${products.filter((p) => p.price).length} prix détectés
• ${products.filter((p) => p.sku).length} références SKU trouvées
• ${products.filter((p) => p.imageUrl).length} images associées
• ${products.reduce(
      (acc, p) => acc + p.characteristics.length,
      0
    )} caractéristiques extraites

🎯 Précision moyenne : ${Math.round(
      (products.reduce((acc, p) => acc + p.confidence, 0) / products.length) *
        100
    )}%
    `;

    setExtractionResults(summary);
  };

  const downloadProductsJSON = () => {
    const jsonData = JSON.stringify(
      {
        catalogue: "Exemple de catalogue",
        extractedAt: new Date().toISOString(),
        totalProducts: extractedProducts.length,
        products: extractedProducts,
      },
      null,
      2
    );

    const blob = new Blob([jsonData], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "produits_extraits.json";
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          🚀 Extraction Automatique de Produits
        </h1>
        <p className="text-lg text-gray-600">
          Transformez vos catalogues PDF en données structurées en quelques
          clics
        </p>
      </div>

      {/* Fonctionnalités */}
      <div className="grid md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
          <div className="flex items-center mb-4">
            <FileText className="h-8 w-8 text-blue-600 mr-3" />
            <h3 className="text-lg font-semibold text-gray-900">
              1. Catalogue PDF
            </h3>
          </div>
          <p className="text-gray-600 mb-4">
            Téléversez votre catalogue PDF pour commencer l'extraction
            automatique.
          </p>
          <button
            onClick={() => setShowCatalogueModal(true)}
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Gérer catalogues
          </button>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
          <div className="flex items-center mb-4">
            <Zap className="h-8 w-8 text-green-600 mr-3" />
            <h3 className="text-lg font-semibold text-gray-900">
              2. Extraction IA
            </h3>
          </div>
          <p className="text-gray-600 mb-4">
            Notre IA analyse le PDF et extrait automatiquement les informations
            produits.
          </p>
          <button
            onClick={() => setShowExtractionModal(true)}
            className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Lancer extraction
          </button>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
          <div className="flex items-center mb-4">
            <Download className="h-8 w-8 text-purple-600 mr-3" />
            <h3 className="text-lg font-semibold text-gray-900">
              3. Export JSON
            </h3>
          </div>
          <p className="text-gray-600 mb-4">
            Exportez les données extraites au format JSON pour vos applications.
          </p>
          <button
            onClick={downloadProductsJSON}
            disabled={extractedProducts.length === 0}
            className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Télécharger JSON
          </button>
        </div>
      </div>

      {/* Résultats de l'extraction */}
      {extractionResults && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <Eye className="h-6 w-6 text-green-600 mr-2" />
            <h3 className="text-lg font-semibold text-green-900">
              Résultats de l'extraction
            </h3>
          </div>
          <pre className="text-sm text-green-800 whitespace-pre-line font-mono bg-green-100 p-4 rounded">
            {extractionResults}
          </pre>
        </div>
      )}

      {/* Produits extraits */}
      {extractedProducts.length > 0 && (
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              Produits extraits ({extractedProducts.length})
            </h3>
          </div>
          <div className="p-6">
            <div className="grid gap-4 max-h-96 overflow-y-auto">
              {extractedProducts.map((product, index) => (
                <div
                  key={product.id}
                  className="border border-gray-200 rounded-lg p-4"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                          #{index + 1}
                        </span>
                        <h4 className="font-medium text-gray-900">
                          {product.name}
                        </h4>
                        <span
                          className={`px-2 py-1 text-xs rounded-full ${
                            product.confidence > 0.8
                              ? "bg-green-100 text-green-800"
                              : product.confidence > 0.6
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {Math.round(product.confidence * 100)}%
                        </span>
                      </div>
                      {product.description && (
                        <p className="text-sm text-gray-600 mb-2">
                          {product.description}
                        </p>
                      )}
                      <div className="flex flex-wrap gap-2 text-sm">
                        {product.price && (
                          <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                            💰 {product.price}
                          </span>
                        )}
                        {product.sku && (
                          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            🏷️ {product.sku}
                          </span>
                        )}
                        <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded">
                          📄 Page {product.pageNumber}
                        </span>
                        {product.characteristics.length > 0 && (
                          <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded">
                            📝 {product.characteristics.length} caractéristiques
                          </span>
                        )}
                      </div>
                    </div>
                    {product.imageUrl && (
                      <img
                        src={product.imageUrl}
                        alt={product.name}
                        className="w-16 h-16 object-cover rounded-lg ml-4"
                      />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">
          💡 Comment utiliser l'extraction automatique
        </h3>
        <div className="space-y-2 text-blue-800">
          <p>
            <strong>1. Préparez votre PDF :</strong> Assurez-vous que votre
            catalogue PDF contient du texte lisible (pas une image scannée).
          </p>
          <p>
            <strong>2. Images optionnelles :</strong> Ajoutez des images de
            produits nommées selon leur nom pour une meilleure correspondance.
          </p>
          <p>
            <strong>3. Vérification :</strong> L'IA extrait automatiquement les
            noms, descriptions, prix et références, mais vérifiez les résultats.
          </p>
          <p>
            <strong>4. Export :</strong> Téléchargez le fichier JSON contenant
            toutes les données structurées.
          </p>
        </div>
      </div>

      {/* Modals */}
      <CatalogueModal
        isOpen={showCatalogueModal}
        onClose={() => setShowCatalogueModal(false)}
        mode="create"
      />

      <ProductExtractionModal
        isOpen={showExtractionModal}
        onClose={() => setShowExtractionModal(false)}
        catalogueName="Catalogue d'exemple"
        onProductsExtracted={handleProductsExtracted}
      />
    </div>
  );
};

export default ProductExtractionExample;
