import { Timestamp } from 'firebase/firestore';
import {
  BaseEntity,
  ValidationResult,
  ValidationError,
  CreateEntity,
  UpdateEntity,
  sanitizeString,
  VALIDATION_LIMITS
} from './common';

// ============================================================================
// MISSION INTERFACES
// ============================================================================

/**
 * Mission status enum - simplified to match database schema
 */
export enum MissionStatus {
  EN_ATTENTE = 'en_attente',
  EN_COURS = 'en_cours',
  TERMINEE = 'terminee',
  ANNULEE = 'annulee'
}

/**
 * Mission priority enum - simplified to match database schema
 */
export enum MissionPriority {
  BASSE = 'basse',
  MOYENNE = 'moyenne',
  HAUTE = 'haute',
  URGENTE = 'urgente'
}

/**
 * Main Mission interface - simplified to match database schema
 */
export interface Mission extends BaseEntity {
  // Basic Information
  titre: string; // Titre de la mission
  description: string; // Description de la mission

  // Assignment Information
  merchandiserId: string; // ID du merchandiser assigné
  commercialId: string; // ID du commercial

  // Client Information
  clientId: string; // ID du client
  clientNom: string; // Nom du client

  // Mission Details
  taches: string; // Tâches à effectuer
  priorite: MissionPriority; // Priorité de la mission
  statut: MissionStatus; // Statut de la mission

  // Dates
  dateCreation: string; // Date de création (format ISO string)
  dateEcheance: string; // Date d'échéance (format ISO string)

  // Additional Information
  id: string; // ID de la mission
  notes: string | null; // Notes additionnelles (peut être null)
  parametres: any | null; // Paramètres additionnels (peut être null)
}
// ============================================================================
// MISSION FORM INTERFACES
// ============================================================================

/**
 * Mission form data interface (for forms)
 */
export interface MissionFormData {
  titre: string;
  description: string;
  merchandiserId: string;
  commercialId: string;
  clientId: string;
  clientNom: string;
  taches: string;
  priorite: MissionPriority;
  statut: MissionStatus;
  dateCreation: string;
  dateEcheance: string;
  notes: string | null;
  parametres: any | null;
}

/**
 * Mission search/filter interface
 */
export interface MissionSearchFilters {
  titre?: string;
  merchandiserId?: string;
  commercialId?: string;
  clientId?: string;
  clientNom?: string;
  statut?: MissionStatus;
  priorite?: MissionPriority;
  dateCreationAfter?: string;
  dateCreationBefore?: string;
  dateEcheanceAfter?: string;
  dateEcheanceBefore?: string;
}

// ============================================================================
// TYPE ALIASES
// ============================================================================

export type CreateMissionData = CreateEntity<Mission>;
export type UpdateMissionData = UpdateEntity<Mission>;
export type MissionWithId = Mission & Required<Pick<Mission, 'id'>>;

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate mission form data
 */
export const validateMissionData = (data: Partial<MissionFormData>): ValidationResult => {
  const errors: ValidationError[] = [];

  // Required fields validation
  if (!data.titre || data.titre.trim().length === 0) {
    errors.push({
      field: 'titre',
      message: 'Le titre est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.description || data.description.trim().length === 0) {
    errors.push({
      field: 'description',
      message: 'La description est requise',
      code: 'REQUIRED'
    });
  }

  if (!data.merchandiserId || data.merchandiserId.trim().length === 0) {
    errors.push({
      field: 'merchandiserId',
      message: 'Le merchandiser est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.commercialId || data.commercialId.trim().length === 0) {
    errors.push({
      field: 'commercialId',
      message: 'Le commercial est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.clientId || data.clientId.trim().length === 0) {
    errors.push({
      field: 'clientId',
      message: 'Le client est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.clientNom || data.clientNom.trim().length === 0) {
    errors.push({
      field: 'clientNom',
      message: 'Le nom du client est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.dateCreation || data.dateCreation.trim().length === 0) {
    errors.push({
      field: 'dateCreation',
      message: 'La date de création est requise',
      code: 'REQUIRED'
    });
  }

  if (!data.dateEcheance || data.dateEcheance.trim().length === 0) {
    errors.push({
      field: 'dateEcheance',
      message: 'La date d\'échéance est requise',
      code: 'REQUIRED'
    });
  }

  // Length validations
  if (data.titre && data.titre.length > VALIDATION_LIMITS.NAME_MAX_LENGTH) {
    errors.push({
      field: 'titre',
      message: `Le titre ne peut pas dépasser ${VALIDATION_LIMITS.NAME_MAX_LENGTH} caractères`,
      code: 'INVALID_LENGTH'
    });
  }

  if (data.description && data.description.length > VALIDATION_LIMITS.DESCRIPTION_MAX_LENGTH) {
    errors.push({
      field: 'description',
      message: `La description ne peut pas dépasser ${VALIDATION_LIMITS.DESCRIPTION_MAX_LENGTH} caractères`,
      code: 'INVALID_LENGTH'
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Sanitize mission data before saving
 */
export const sanitizeMissionData = (data: MissionFormData): MissionFormData => {
  return {
    ...data,
    titre: sanitizeString(data.titre),
    description: sanitizeString(data.description),
    merchandiserId: sanitizeString(data.merchandiserId),
    commercialId: sanitizeString(data.commercialId),
    clientId: sanitizeString(data.clientId),
    clientNom: sanitizeString(data.clientNom),
    taches: sanitizeString(data.taches),
    dateCreation: data.dateCreation.trim(),
    dateEcheance: data.dateEcheance.trim(),
    notes: data.notes ? sanitizeString(data.notes) : null
  };
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get mission display name
 */
export const getMissionDisplayName = (mission: Mission): string => {
  return `${mission.titre} - ${mission.clientNom}`;
};

/**
 * Get mission status display text
 */
export const getMissionStatusText = (status: MissionStatus): string => {
  const statusMap = {
    [MissionStatus.EN_ATTENTE]: 'En attente',
    [MissionStatus.EN_COURS]: 'En cours',
    [MissionStatus.TERMINEE]: 'Terminée',
    [MissionStatus.ANNULEE]: 'Annulée'
  };

  return statusMap[status] || status;
};

/**
 * Get mission priority display text
 */
export const getMissionPriorityText = (priority: MissionPriority): string => {
  const priorityMap = {
    [MissionPriority.BASSE]: 'Basse',
    [MissionPriority.MOYENNE]: 'Moyenne',
    [MissionPriority.HAUTE]: 'Haute',
    [MissionPriority.URGENTE]: 'Urgente'
  };

  return priorityMap[priority] || priority;
};

/**
 * Check if mission is overdue
 */
export const isMissionOverdue = (mission: Mission): boolean => {
  if (mission.statut === MissionStatus.TERMINEE || mission.statut === MissionStatus.ANNULEE) {
    return false;
  }

  const now = new Date();
  const echeance = new Date(mission.dateEcheance);
  return echeance < now;
};

/**
 * Check if mission can be modified
 */
export const canModifyMission = (mission: Mission): boolean => {
  return mission.statut === MissionStatus.EN_ATTENTE || mission.statut === MissionStatus.EN_COURS;
};

/**
 * Format mission date
 */
export const formatMissionDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('fr-FR');
};
