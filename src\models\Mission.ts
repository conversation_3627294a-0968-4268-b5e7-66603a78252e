import { Timestamp } from 'firebase/firestore';
import { 
  BaseEntity, 
  EntityStatus, 
  Priority,
  AuditTrail, 
  Metadata,
  ValidationResult,
  ValidationError,
  CreateEntity,
  UpdateEntity,
  isValidEmail,
  sanitizeString,
  VALIDATION_LIMITS
} from './common';

// ============================================================================
// MISSION INTERFACES
// ============================================================================

/**
 * Mission status enum
 */
export enum MissionStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  ON_HOLD = 'on_hold'
}

/**
 * Mission type enum
 */
export enum MissionType {
  VISIT = 'visit',
  DELIVERY = 'delivery',
  MAINTENANCE = 'maintenance',
  SURVEY = 'survey',
  TRAINING = 'training',
  AUDIT = 'audit'
}

/**
 * Location information
 */
export interface MissionLocation {
  latitude: number;
  longitude: number;
  address: string;
  city?: string;
  postalCode?: string;
  country?: string;
}

/**
 * Mission attachment interface
 */
export interface MissionAttachment {
  id: string;
  fileName: string;
  fileUrl: string;
  fileType: 'image' | 'document' | 'video' | 'audio';
  fileSize: number;
  uploadedAt: Timestamp;
  uploadedBy: string;
}

/**
 * Mission task interface
 */
export interface MissionTask {
  id: string;
  title: string;
  description?: string;
  isCompleted: boolean;
  completedAt?: Timestamp;
  completedBy?: string;
  notes?: string;
}

/**
 * Main Mission interface
 */
export interface Mission extends BaseEntity {
  // Basic Information
  title: string;
  description: string;
  type: MissionType;
  
  // Assignment Information
  merchandizerId: string;
  merchandizerName: string;
  assignedBy: string;
  assignedAt: Timestamp;
  
  // Client Information
  clientId: string;
  clientName: string;
  clientAddress?: string;
  
  // Scheduling
  missionDate: Timestamp;
  startTime: string; // Format: "HH:MM"
  endTime?: string; // Format: "HH:MM"
  estimatedDuration?: number; // in minutes
  actualStartTime?: Timestamp;
  actualEndTime?: Timestamp;
  
  // Status and Priority
  status: MissionStatus;
  priority: Priority;
  
  // Tasks and Completion
  tasks: MissionTask[];
  completionPercentage: number;
  completedAt?: Timestamp;
  completionNotes?: string;
  
  // Location
  location?: MissionLocation;
  
  // Attachments and Documentation
  attachments: MissionAttachment[];
  photos: string[]; // URLs to photos
  documents: string[]; // URLs to documents
  
  // Notes and Comments
  notes?: string;
  internalNotes?: string;
  clientFeedback?: string;
  
  // Metadata
  metadata?: Metadata;
  
  // Audit Information
  audit: AuditTrail;
}

// ============================================================================
// MISSION FORM INTERFACES
// ============================================================================

/**
 * Mission form data interface (for forms)
 */
export interface MissionFormData {
  title: string;
  description: string;
  type: MissionType;
  merchandizerId: string;
  clientId: string;
  missionDate: string; // ISO date string for forms
  startTime: string;
  endTime?: string;
  estimatedDuration?: number;
  priority: Priority;
  tasks: string[]; // Simple string array for forms
  notes?: string;
  location?: {
    latitude: number;
    longitude: number;
    address: string;
  };
}

/**
 * Mission search/filter interface
 */
export interface MissionSearchFilters {
  title?: string;
  merchandizerId?: string;
  clientId?: string;
  status?: MissionStatus;
  type?: MissionType;
  priority?: Priority;
  dateFrom?: Timestamp;
  dateTo?: Timestamp;
  assignedBy?: string;
  location?: string;
}

// ============================================================================
// MISSION UTILITY TYPES
// ============================================================================

/**
 * Create mission data type
 */
export type CreateMissionData = CreateEntity<Mission>;

/**
 * Update mission data type
 */
export type UpdateMissionData = UpdateEntity<Mission>;

/**
 * Mission with required ID
 */
export type MissionWithId = Mission & Required<Pick<Mission, 'id'>>;

// ============================================================================
// MISSION VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate mission data
 */
export const validateMissionData = (data: Partial<MissionFormData>): ValidationResult => {
  const errors: ValidationError[] = [];

  // Required fields validation
  if (!data.title?.trim()) {
    errors.push({ field: 'title', message: 'Le titre est requis' });
  } else if (data.title.length > VALIDATION_LIMITS.TITLE_MAX_LENGTH) {
    errors.push({ field: 'title', message: `Le titre ne peut pas dépasser ${VALIDATION_LIMITS.TITLE_MAX_LENGTH} caractères` });
  }

  if (!data.description?.trim()) {
    errors.push({ field: 'description', message: 'La description est requise' });
  } else if (data.description.length > VALIDATION_LIMITS.DESCRIPTION_MAX_LENGTH) {
    errors.push({ field: 'description', message: `La description ne peut pas dépasser ${VALIDATION_LIMITS.DESCRIPTION_MAX_LENGTH} caractères` });
  }

  if (!data.merchandizerId?.trim()) {
    errors.push({ field: 'merchandizerId', message: 'Le merchandiser est requis' });
  }

  if (!data.clientId?.trim()) {
    errors.push({ field: 'clientId', message: 'Le client est requis' });
  }

  if (!data.missionDate?.trim()) {
    errors.push({ field: 'missionDate', message: 'La date de mission est requise' });
  }

  if (!data.startTime?.trim()) {
    errors.push({ field: 'startTime', message: 'L\'heure de début est requise' });
  }

  // Time validation
  if (data.startTime && data.endTime) {
    const start = new Date(`2000-01-01T${data.startTime}:00`);
    const end = new Date(`2000-01-01T${data.endTime}:00`);
    if (end <= start) {
      errors.push({ field: 'endTime', message: 'L\'heure de fin doit être après l\'heure de début' });
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Sanitize mission data
 */
export const sanitizeMissionData = (data: Partial<MissionFormData>): Partial<MissionFormData> => {
  return {
    ...data,
    title: data.title ? sanitizeString(data.title) : undefined,
    description: data.description ? sanitizeString(data.description) : undefined,
    notes: data.notes ? sanitizeString(data.notes) : undefined,
    tasks: data.tasks?.map(task => sanitizeString(task)).filter(Boolean) || []
  };
};

// ============================================================================
// MISSION UTILITY FUNCTIONS
// ============================================================================

/**
 * Get mission display name
 */
export const getMissionDisplayName = (mission: Mission): string => {
  return `${mission.title} - ${mission.clientName}`;
};

/**
 * Calculate mission duration in minutes
 */
export const calculateMissionDuration = (mission: Mission): number | null => {
  if (!mission.actualStartTime || !mission.actualEndTime) {
    return null;
  }
  
  const start = mission.actualStartTime.toDate();
  const end = mission.actualEndTime.toDate();
  return Math.round((end.getTime() - start.getTime()) / (1000 * 60));
};

/**
 * Check if mission is overdue
 */
export const isMissionOverdue = (mission: Mission): boolean => {
  if (mission.status === MissionStatus.COMPLETED || mission.status === MissionStatus.CANCELLED) {
    return false;
  }
  
  const now = new Date();
  const missionDate = mission.missionDate.toDate();
  return missionDate < now;
};

/**
 * Get mission status color
 */
export const getMissionStatusColor = (status: MissionStatus): string => {
  switch (status) {
    case MissionStatus.PENDING:
      return 'yellow';
    case MissionStatus.IN_PROGRESS:
      return 'blue';
    case MissionStatus.COMPLETED:
      return 'green';
    case MissionStatus.CANCELLED:
      return 'red';
    case MissionStatus.ON_HOLD:
      return 'gray';
    default:
      return 'gray';
  }
};

/**
 * Calculate completion percentage based on tasks
 */
export const calculateCompletionPercentage = (tasks: MissionTask[]): number => {
  if (tasks.length === 0) return 0;
  
  const completedTasks = tasks.filter(task => task.isCompleted).length;
  return Math.round((completedTasks / tasks.length) * 100);
};
