import React, { useState, useEffect } from "react";
import {
  X,
  Save,
  Loader2,
  Upload,
  Download,
  FileText,
  Zap,
} from "lucide-react";
import { Catalogue, CatalogueType } from "../../models";
import { useApp } from "../../hooks/useApp";
import ProductExtractionModal from "./ProductExtractionModal";
import { ExtractedProduct } from "../../services/pdfExtractor";

interface CatalogueModalProps {
  isOpen: boolean;
  onClose: () => void;
  catalogue?: Catalogue | null;
  mode: "create" | "edit" | "view";
}

const CatalogueModal: React.FC<CatalogueModalProps> = ({
  isOpen,
  onClose,
  catalogue,
  mode,
}) => {
  const { addCatalogue, updateCatalogue } = useApp();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showExtractionModal, setShowExtractionModal] = useState(false);
  const [extractedProducts, setExtractedProducts] = useState<
    ExtractedProduct[]
  >([]);

  const [formData, setFormData] = useState({
    nom: "",
    description: "",
    type: CatalogueType.PRODUITS,
    urlPdf: "",
    cheminLocal: null as string | null,
    imagePreview: null as string | null,
    isActive: true,
    metadata: null as any | null,
  });

  const catalogueTypes = [
    { value: CatalogueType.PRODUITS, label: "Produits" },
    { value: CatalogueType.SERVICES, label: "Services" },
    { value: CatalogueType.PROMOTIONS, label: "Promotions" },
  ];

  useEffect(() => {
    if (catalogue && (mode === "edit" || mode === "view")) {
      setFormData({
        nom: catalogue.nom || "",
        description: catalogue.description || "",
        type: catalogue.type || CatalogueType.PRODUITS,
        urlPdf: catalogue.urlPdf || "",
        cheminLocal: catalogue.cheminLocal || null,
        imagePreview: catalogue.imagePreview || null,
        isActive: catalogue.isActive !== undefined ? catalogue.isActive : true,
        metadata: catalogue.metadata || null,
      });
    } else {
      setFormData({
        nom: "",
        description: "",
        type: CatalogueType.PRODUITS,
        urlPdf: "",
        cheminLocal: null,
        imagePreview: null,
        isActive: true,
        metadata: null,
      });
    }
    setError(null);
  }, [catalogue, mode, isOpen]);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "number" ? parseInt(value) || 0 : value,
    }));
  };

  const validateForm = () => {
    if (!formData.nom.trim()) {
      setError("Le nom du catalogue est requis");
      return false;
    }
    if (!formData.description.trim()) {
      setError("La description est requise");
      return false;
    }
    if (formData.urlPdf && !isValidUrl(formData.urlPdf)) {
      setError("L'URL du PDF doit être valide");
      return false;
    }
    return true;
  };

  const isValidUrl = (string: string) => {
    try {
      new URL(string);
      return true;
    } catch {
      return false;
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Here you would typically upload the file to Firebase Storage
      // and get the download URL
      const fileSizeInMB = (file.size / (1024 * 1024)).toFixed(2);

      // For now, create a temporary URL for the file
      const tempUrl = URL.createObjectURL(file);

      setFormData((prev) => ({
        ...prev,
        urlPdf: tempUrl, // In production, this would be the Firebase Storage URL
        cheminLocal: file.name,
      }));

      // Placeholder for file upload logic
      console.log("File selected:", file.name, "Size:", fileSizeInMB, "MB");
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (mode === "view") return;

    if (!validateForm()) return;

    setLoading(true);
    setError(null);

    try {
      // Clean form data to ensure no undefined values
      const catalogueData = {
        nom: formData.nom.trim(),
        description: formData.description.trim(),
        type: formData.type,
        urlPdf: formData.urlPdf.trim(),
        cheminLocal: formData.cheminLocal,
        imagePreview: formData.imagePreview,
        isActive: formData.isActive,
        metadata: {
          ...formData.metadata,
          // Ajouter les produits extraits s'ils existent
          ...(extractedProducts.length > 0 && {
            extractedProducts: extractedProducts.map((p) => ({
              name: p.name,
              description: p.description,
              characteristics: p.characteristics,
              price: p.price,
              sku: p.sku,
              confidence: p.confidence,
            })),
          }),
        },
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString(),
      };

      if (mode === "create") {
        await addCatalogue(catalogueData);
      } else if (mode === "edit" && catalogue?.id) {
        await updateCatalogue(catalogue.id, {
          ...catalogueData,
          dateModification: new Date().toISOString(),
        });
      }

      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Une erreur est survenue");
    } finally {
      setLoading(false);
    }
  };

  const handleProductsExtracted = (products: ExtractedProduct[]) => {
    setExtractedProducts(products);
    setShowExtractionModal(false);
  };

  const downloadProductsJSON = () => {
    if (extractedProducts.length === 0) return;

    const jsonData = JSON.stringify(
      {
        catalogue: formData.nom,
        extractedAt: new Date().toISOString(),
        totalProducts: extractedProducts.length,
        products: extractedProducts,
      },
      null,
      2
    );

    const blob = new Blob([jsonData], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${formData.nom.replace(/[^a-z0-9]/gi, "_")}_products.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  if (!isOpen) return null;

  const isReadOnly = mode === "view";
  const title =
    mode === "create"
      ? "Nouveau Catalogue"
      : mode === "edit"
      ? "Modifier Catalogue"
      : "Détails Catalogue";

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form
          onSubmit={handleSubmit}
          className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]"
        >
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <label
                htmlFor="nom"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Nom du catalogue *
              </label>
              <input
                type="text"
                id="nom"
                name="nom"
                value={formData.nom}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                required
                placeholder="ex: Catalogue Printemps 2024"
              />
            </div>

            <div className="md:col-span-2">
              <label
                htmlFor="description"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Description *
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                disabled={isReadOnly}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                required
                placeholder="Description détaillée du catalogue..."
              />
            </div>

            <div>
              <label
                htmlFor="type"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Type *
              </label>
              <select
                id="type"
                name="type"
                value={formData.type}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                required
              >
                {catalogueTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      isActive: e.target.checked,
                    }))
                  }
                  disabled={isReadOnly}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-gray-700">
                  Catalogue actif
                </span>
              </label>
            </div>

            <div className="md:col-span-2">
              <label
                htmlFor="urlPdf"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                URL du PDF *
              </label>
              <div className="flex">
                <input
                  type="url"
                  id="urlPdf"
                  name="urlPdf"
                  value={formData.urlPdf}
                  onChange={handleInputChange}
                  disabled={isReadOnly}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                  placeholder="https://example.com/catalogue.pdf"
                  required
                />
                {!isReadOnly && (
                  <label className="px-3 py-2 bg-gray-200 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-300 transition-colors cursor-pointer flex items-center">
                    <Upload className="h-4 w-4" />
                    <input
                      type="file"
                      onChange={handleFileUpload}
                      accept=".pdf"
                      className="hidden"
                    />
                  </label>
                )}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Format accepté: PDF uniquement
              </p>
            </div>

            <div>
              <label
                htmlFor="cheminLocal"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Chemin local (optionnel)
              </label>
              <input
                type="text"
                id="cheminLocal"
                name="cheminLocal"
                value={formData.cheminLocal || ""}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                placeholder="ex: /uploads/catalogues/catalogue.pdf"
              />
            </div>

            <div>
              <label
                htmlFor="imagePreview"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Image de prévisualisation (optionnel)
              </label>
              <input
                type="url"
                id="imagePreview"
                name="imagePreview"
                value={formData.imagePreview || ""}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                placeholder="https://example.com/preview.jpg"
              />
            </div>

            {formData.urlPdf && (
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Aperçu du fichier
                </label>
                <div className="border border-gray-300 rounded-md p-4 bg-gray-50">
                  <div className="flex items-center space-x-3">
                    <FileText className="h-8 w-8 text-blue-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {formData.nom || "Catalogue"}
                      </p>
                      <p className="text-xs text-gray-500">
                        PDF - {formData.isActive ? "Actif" : "Inactif"}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      {formData.urlPdf && (
                        <a
                          href={formData.urlPdf}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
                        >
                          <Download className="h-4 w-4" />
                          <span>Télécharger</span>
                        </a>
                      )}
                      {!isReadOnly &&
                        formData.urlPdf.toLowerCase().endsWith(".pdf") && (
                          <button
                            type="button"
                            onClick={() => setShowExtractionModal(true)}
                            className="flex items-center space-x-1 px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm"
                          >
                            <Zap className="h-4 w-4" />
                            <span>Extraire produits</span>
                          </button>
                        )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Section produits extraits */}
            {extractedProducts.length > 0 && (
              <div className="md:col-span-2">
                <div className="flex items-center justify-between mb-3">
                  <label className="block text-sm font-medium text-gray-700">
                    Produits extraits ({extractedProducts.length})
                  </label>
                  <button
                    type="button"
                    onClick={downloadProductsJSON}
                    className="flex items-center space-x-1 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                  >
                    <Download className="h-3 w-3" />
                    <span>JSON</span>
                  </button>
                </div>
                <div className="border border-gray-300 rounded-md p-4 bg-gray-50 max-h-48 overflow-y-auto">
                  <div className="space-y-2">
                    {extractedProducts.slice(0, 5).map((product) => (
                      <div
                        key={product.id}
                        className="flex items-center justify-between p-2 bg-white rounded border"
                      >
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">
                            {product.name}
                          </p>
                          <p className="text-xs text-gray-500 truncate">
                            {product.description}
                          </p>
                          {product.price && (
                            <p className="text-xs text-green-600 font-medium">
                              {product.price}
                            </p>
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          <span
                            className={`px-2 py-1 text-xs rounded-full ${
                              product.confidence > 0.8
                                ? "bg-green-100 text-green-800"
                                : product.confidence > 0.6
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {Math.round(product.confidence * 100)}%
                          </span>
                          {product.imageUrl && (
                            <img
                              src={product.imageUrl}
                              alt={product.name}
                              className="w-8 h-8 object-cover rounded"
                            />
                          )}
                        </div>
                      </div>
                    ))}
                    {extractedProducts.length > 5 && (
                      <p className="text-center text-xs text-gray-500 py-2">
                        ... et {extractedProducts.length - 5} autres produits
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {!isReadOnly && (
            <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
              >
                Annuler
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
              >
                {loading && <Loader2 className="h-4 w-4 animate-spin" />}
                <Save className="h-4 w-4" />
                <span>{mode === "create" ? "Créer" : "Sauvegarder"}</span>
              </button>
            </div>
          )}
        </form>
      </div>

      {/* Modal d'extraction de produits */}
      <ProductExtractionModal
        isOpen={showExtractionModal}
        onClose={() => setShowExtractionModal(false)}
        catalogueName={formData.nom}
        onProductsExtracted={handleProductsExtracted}
      />
    </div>
  );
};

export default CatalogueModal;
