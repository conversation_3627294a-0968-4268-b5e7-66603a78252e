import { Timestamp } from 'firebase/firestore';
import {
  BaseEntity,
  ValidationResult,
  ValidationError,
  CreateEntity,
  UpdateEntity,
  sanitizeString,
  VALIDATION_LIMITS
} from './common';

// ============================================================================
// ORDER INTERFACES
// ============================================================================

/**
 * Order status enum - simplified to match database schema
 */
export enum OrderStatus {
  CONFIRMEE = 'confirmee',
  EN_COURS = 'en_cours',
  LIVREE = 'livree',
  ANNULEE = 'annulee'
}

/**
 * Order line item interface - simplified to match database schema
 */
export interface OrderLineItem {
  codeProduit: string; // Code du produit
  nomProduit: string; // Nom du produit
  prixUnitaire: number; // Prix unitaire
  produitId: string; // ID du produit
  quantite: number; // Quantité
  sousTotal: number; // Sous-total
  unite: string; // Unité (pièce, kg, etc.)
}

/**
 * Main Order interface - simplified to match database schema (commandes collection)
 */
export interface Order extends BaseEntity {
  // Basic Information
  clientId: string; // ID du client
  dateCommande: string; // Date de commande (format ISO string)

  // Items
  items: OrderLineItem[]; // Articles de la commande

  // Totals
  montantTotal: number; // Montant total

  // Status
  statut: OrderStatus; // Statut de la commande
}
// ============================================================================
// ORDER FORM INTERFACES
// ============================================================================

/**
 * Order form data interface (for forms)
 */
export interface OrderFormData {
  clientId: string;
  dateCommande: string;
  items: OrderLineItem[];
  montantTotal: number;
  statut: OrderStatus;
}

/**
 * Order search/filter interface
 */
export interface OrderSearchFilters {
  clientId?: string;
  statut?: OrderStatus;
  dateCommandeAfter?: string;
  dateCommandeBefore?: string;
  montantTotalMin?: number;
  montantTotalMax?: number;
}

// ============================================================================
// TYPE ALIASES
// ============================================================================

export type CreateOrderData = CreateEntity<Order>;
export type UpdateOrderData = UpdateEntity<Order>;
export type OrderWithId = Order & Required<Pick<Order, 'id'>>;

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate order form data
 */
export const validateOrderData = (data: Partial<OrderFormData>): ValidationResult => {
  const errors: ValidationError[] = [];

  // Required fields validation
  if (!data.clientId || data.clientId.trim().length === 0) {
    errors.push({
      field: 'clientId',
      message: 'L\'ID du client est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.dateCommande || data.dateCommande.trim().length === 0) {
    errors.push({
      field: 'dateCommande',
      message: 'La date de commande est requise',
      code: 'REQUIRED'
    });
  }

  if (!data.items || data.items.length === 0) {
    errors.push({
      field: 'items',
      message: 'Au moins un article est requis',
      code: 'REQUIRED'
    });
  }

  if (data.montantTotal === undefined || data.montantTotal < 0) {
    errors.push({
      field: 'montantTotal',
      message: 'Le montant total doit être positif',
      code: 'INVALID_VALUE'
    });
  }

  // Validate items
  if (data.items) {
    data.items.forEach((item, index) => {
      if (!item.codeProduit || item.codeProduit.trim().length === 0) {
        errors.push({
          field: `items[${index}].codeProduit`,
          message: 'Le code produit est requis',
          code: 'REQUIRED'
        });
      }

      if (!item.nomProduit || item.nomProduit.trim().length === 0) {
        errors.push({
          field: `items[${index}].nomProduit`,
          message: 'Le nom du produit est requis',
          code: 'REQUIRED'
        });
      }

      if (item.quantite <= 0) {
        errors.push({
          field: `items[${index}].quantite`,
          message: 'La quantité doit être positive',
          code: 'INVALID_VALUE'
        });
      }

      if (item.prixUnitaire < 0) {
        errors.push({
          field: `items[${index}].prixUnitaire`,
          message: 'Le prix unitaire ne peut pas être négatif',
          code: 'INVALID_VALUE'
        });
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};
/**
 * Sanitize order data before saving
 */
export const sanitizeOrderData = (data: OrderFormData): OrderFormData => {
  return {
    ...data,
    clientId: sanitizeString(data.clientId),
    dateCommande: data.dateCommande.trim(),
    items: data.items.map(item => ({
      ...item,
      codeProduit: sanitizeString(item.codeProduit),
      nomProduit: sanitizeString(item.nomProduit),
      produitId: sanitizeString(item.produitId),
      unite: sanitizeString(item.unite)
    }))
  };
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Calculate order total from items
 */
export const calculateOrderTotal = (items: OrderLineItem[]): number => {
  return items.reduce((total, item) => total + item.sousTotal, 0);
};

/**
 * Get order status display text
 */
export const getOrderStatusText = (status: OrderStatus): string => {
  const statusMap = {
    [OrderStatus.CONFIRMEE]: 'Confirmée',
    [OrderStatus.EN_COURS]: 'En cours',
    [OrderStatus.LIVREE]: 'Livrée',
    [OrderStatus.ANNULEE]: 'Annulée'
  };

  return statusMap[status] || status;
};

/**
 * Check if order can be cancelled
 */
export const canCancelOrder = (order: Order): boolean => {
  return order.statut === OrderStatus.CONFIRMEE || order.statut === OrderStatus.EN_COURS;
};

/**
 * Check if order can be modified
 */
export const canModifyOrder = (order: Order): boolean => {
  return order.statut === OrderStatus.CONFIRMEE;
};

/**
 * Format order date
 */
export const formatOrderDate = (dateCommande: string): string => {
  return new Date(dateCommande).toLocaleDateString('fr-FR');
};


