import React, { useState, useEffect } from "react";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  MapPin,
  Calendar,
  Eye,
  Filter,
  TrendingUp,
  Users,
} from "lucide-react";
import { useApp } from "../hooks/useApp";
import { MerchandizerModal } from "../components/modals";
import { formatDate } from "../models";

const MerchandizerManagement: React.FC = () => {
  const { merchandizers, merchandizersLoading, deleteMerchandizer } = useApp();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<
    "all" | "active" | "inactive"
  >("all");
  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    mode: "create" | "edit" | "view";
    merchandizer?: any;
  }>({ isOpen: false, mode: "create" });

  // Filter merchandizers based on search and status
  const filteredMerchandizers = merchandizers.filter((merchandizer) => {
    const matchesSearch =
      merchandizer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      merchandizer.nomComplet
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      merchandizer.territoire
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      merchandizer.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === "all" || merchandizer.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Modal handlers
  const openModal = (mode: "create" | "edit" | "view", merchandizer?: any) => {
    setModalState({ isOpen: true, mode, merchandizer });
  };

  const closeModal = () => {
    setModalState({ isOpen: false, mode: "create" });
  };

  const handleDelete = async (id: string) => {
    if (
      window.confirm("Êtes-vous sûr de vouloir supprimer ce merchandizer ?")
    ) {
      try {
        await deleteMerchandizer(id);
      } catch (error) {
        console.error("Error deleting merchandizer:", error);
        alert("Erreur lors de la suppression du merchandizer");
      }
    }
  };

  const getPerformanceColor = (performance: number) => {
    if (performance >= 90) return "text-green-600";
    if (performance >= 80) return "text-yellow-600";
    return "text-red-600";
  };

  const getPerformanceBg = (performance: number) => {
    if (performance >= 90) return "bg-green-100";
    if (performance >= 80) return "bg-yellow-100";
    return "bg-red-100";
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">
          Gestion des Merchandiseurs
        </h1>
        <button
          onClick={() => openModal("create")}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Ajouter un Merchandiseur</span>
        </button>
      </div>

      {/* Search and Filter Bar */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Rechercher des merchandiseurs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <select
              value={statusFilter}
              onChange={(e) =>
                setStatusFilter(e.target.value as "all" | "active" | "inactive")
              }
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">Tous les statuts</option>
              <option value="active">Actif</option>
              <option value="inactive">Inactif</option>
            </select>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {merchandizersLoading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">
            Chargement des merchandiseurs...
          </span>
        </div>
      ) : (
        <>
          {/* Merchandizers Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredMerchandizers.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  Aucun merchandiser trouvé
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm || statusFilter !== "all"
                    ? "Essayez de modifier vos critères de recherche."
                    : "Commencez par ajouter un nouveau merchandiser."}
                </p>
              </div>
            ) : (
              filteredMerchandizers.map((merchandizer) => (
                <div
                  key={merchandizer.id}
                  className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {merchandizer.nomComplet}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {merchandizer.email}
                      </p>
                      <p className="text-sm text-gray-600">
                        {merchandizer.telephone}
                      </p>
                    </div>
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        merchandizer.status === "active"
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {merchandizer.status === "active" ? "Actif" : "Inactif"}
                    </span>
                  </div>

                  <div className="space-y-3 mb-4">
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-2" />
                      <span>{merchandizer.territoire}</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="h-4 w-4 mr-2" />
                      <span>
                        Dernière connexion:{" "}
                        {merchandizer.lastLogin
                          ? formatDate(merchandizer.lastLogin)
                          : "Jamais connecté"}
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-sm font-medium text-gray-900">
                        {merchandizer.mobile || "N/A"}
                      </div>
                      <div className="text-xs text-gray-500">Mobile</div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm font-medium text-gray-900">
                        {merchandizer.userType}
                      </div>
                      <div className="text-xs text-gray-500">Type</div>
                    </div>
                  </div>
                      <div
                        className={`h-2 rounded-full ${getPerformanceBg(
                          merchandizer.performance
                        )} ${getPerformanceColor(
                          merchandizer.performance
                        ).replace("text-", "bg-")}`}
                        style={{ width: `${merchandizer.performance}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <button
                      onClick={() => openModal("view", merchandizer)}
                      className="flex-1 bg-gray-50 text-gray-600 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors flex items-center justify-center space-x-1"
                    >
                      <Eye className="h-4 w-4" />
                      <span>Voir</span>
                    </button>
                    <button
                      onClick={() => openModal("edit", merchandizer)}
                      className="flex-1 bg-blue-50 text-blue-600 px-3 py-2 rounded-lg hover:bg-blue-100 transition-colors flex items-center justify-center space-x-1"
                    >
                      <Edit className="h-4 w-4" />
                      <span>Modifier</span>
                    </button>
                    <button
                      onClick={() => handleDelete(merchandizer.id!)}
                      className="bg-red-50 text-red-600 px-3 py-2 rounded-lg hover:bg-red-100 transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>
        </>
      )}

      {/* Merchandizer Modal */}
      <MerchandizerModal
        isOpen={modalState.isOpen}
        onClose={closeModal}
        merchandizer={modalState.merchandizer}
        mode={modalState.mode}
      />
    </div>
  );
};

export default MerchandizerManagement;
