import { Timestamp } from 'firebase/firestore';
import {
  BaseEntity,
  ValidationResult,
  ValidationError,
  CreateEntity,
  UpdateEntity,
  isValidEmail,
  isValidPhone,
  sanitizeString,
  VALIDATION_LIMITS
} from './common';

// ============================================================================
// COMMERCIAL INTERFACES
// ============================================================================

/**
 * Commercial status enum - simplified to match database schema
 */
export enum CommercialStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive'
}

/**
 * Commercial role enumeration
 */
export enum CommercialRole {
  COMMERCIAL = 'commercial',
  MANAGER = 'manager',
  DIRECTOR = 'director'
}

/**
 * Employment type enumeration
 */
export enum EmploymentType {
  FULL_TIME = 'full_time',
  PART_TIME = 'part_time',
  CONTRACT = 'contract'
}

/**
 * Territory type enumeration
 */
export enum TerritoryType {
  URBAN = 'urban',
  RURAL = 'rural',
  MIXED = 'mixed'
}

/**
 * Commercial mobile permission enumeration
 */
export enum CommercialMobilePermission {
  FULL_ACCESS = 'full_access',
  LIMITED_ACCESS = 'limited_access',
  NO_ACCESS = 'no_access'
}

/**
 * Main Commercial interface - simplified to match database schema
 */
export interface Commercial extends BaseEntity {
  // Basic Information
  name: string; // Nom du commercial
  nomComplet: string; // Nom complet du commercial
  email: string; // Email du commercial

  // Contact Information
  mobile: string; // Téléphone mobile
  telephone: string; // Téléphone fixe

  // Territory and Status
  territoire: string; // Territoire assigné
  status: CommercialStatus; // Statut du commercial

  // Authentication
  uid: string; // UID Firebase Auth
  userType: string; // Type d'utilisateur (commercial)

  // Dates
  createdAt: Timestamp; // Date de création
  lastLogin: Timestamp; // Dernière connexion
}

// ============================================================================
// COMMERCIAL FORM INTERFACES
// ============================================================================

/**
 * Commercial form data interface (for forms)
 */
export interface CommercialFormData {
  name: string;
  nomComplet: string;
  email: string;
  mobile: string;
  telephone: string;
  territoire: string;
  status: CommercialStatus;
  uid: string;
  userType: string;
}

/**
 * Commercial search/filter interface
 */
export interface CommercialSearchFilters {
  name?: string;
  nomComplet?: string;
  email?: string;
  mobile?: string;
  telephone?: string;
  territoire?: string;
  status?: CommercialStatus;
  userType?: string;
  createdAtAfter?: Timestamp;
  createdAtBefore?: Timestamp;
  lastLoginAfter?: Timestamp;
  lastLoginBefore?: Timestamp;
}

// ============================================================================
// TYPE ALIASES
// ============================================================================

export type CreateCommercialData = CreateEntity<Commercial>;
export type UpdateCommercialData = UpdateEntity<Commercial>;
export type CommercialWithId = Commercial & Required<Pick<Commercial, 'id'>>;

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate commercial form data
 */
export const validateCommercialData = (data: Partial<CommercialFormData>): ValidationResult => {
  const errors: ValidationError[] = [];

  // Required fields validation
  if (!data.name || data.name.trim().length === 0) {
    errors.push({
      field: 'name',
      message: 'Le nom est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.nomComplet || data.nomComplet.trim().length === 0) {
    errors.push({
      field: 'nomComplet',
      message: 'Le nom complet est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.email || data.email.trim().length === 0) {
    errors.push({
      field: 'email',
      message: 'L\'email est requis',
      code: 'REQUIRED'
    });
  } else if (!isValidEmail(data.email)) {
    errors.push({
      field: 'email',
      message: 'Format d\'email invalide',
      code: 'INVALID_FORMAT'
    });
  }

  if (!data.mobile || data.mobile.trim().length === 0) {
    errors.push({
      field: 'mobile',
      message: 'Le mobile est requis',
      code: 'REQUIRED'
    });
  } else if (!isValidPhone(data.mobile)) {
    errors.push({
      field: 'mobile',
      message: 'Format de mobile invalide',
      code: 'INVALID_FORMAT'
    });
  }

  if (!data.telephone || data.telephone.trim().length === 0) {
    errors.push({
      field: 'telephone',
      message: 'Le téléphone est requis',
      code: 'REQUIRED'
    });
  } else if (!isValidPhone(data.telephone)) {
    errors.push({
      field: 'telephone',
      message: 'Format de téléphone invalide',
      code: 'INVALID_FORMAT'
    });
  }

  if (!data.territoire || data.territoire.trim().length === 0) {
    errors.push({
      field: 'territoire',
      message: 'Le territoire est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.uid || data.uid.trim().length === 0) {
    errors.push({
      field: 'uid',
      message: 'L\'UID est requis',
      code: 'REQUIRED'
    });
  }

  // Length validations
  if (data.name && data.name.length > VALIDATION_LIMITS.NAME_MAX_LENGTH) {
    errors.push({
      field: 'name',
      message: `Le nom ne peut pas dépasser ${VALIDATION_LIMITS.NAME_MAX_LENGTH} caractères`,
      code: 'INVALID_LENGTH'
    });
  }

  if (data.email && data.email.length > VALIDATION_LIMITS.EMAIL_MAX_LENGTH) {
    errors.push({
      field: 'email',
      message: `L'email ne peut pas dépasser ${VALIDATION_LIMITS.EMAIL_MAX_LENGTH} caractères`,
      code: 'INVALID_LENGTH'
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};
/**
 * Sanitize commercial data before saving
 */
export const sanitizeCommercialData = (data: CommercialFormData): CommercialFormData => {
  return {
    ...data,
    name: sanitizeString(data.name),
    nomComplet: sanitizeString(data.nomComplet),
    email: data.email.toLowerCase().trim(),
    mobile: data.mobile.replace(/[\s\-\(\)]/g, ''),
    telephone: data.telephone.replace(/[\s\-\(\)]/g, ''),
    territoire: sanitizeString(data.territoire),
    uid: sanitizeString(data.uid),
    userType: sanitizeString(data.userType)
  };
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get commercial display name
 */
export const getCommercialDisplayName = (commercial: Commercial): string => {
  return commercial.nomComplet || commercial.name;
};

/**
 * Check if commercial is active
 */
export const isCommercialActive = (commercial: Commercial): boolean => {
  return commercial.status === CommercialStatus.ACTIVE;
};

/**
 * Format commercial territory
 */
export const formatCommercialTerritory = (territoire: string): string => {
  return territoire.toUpperCase();
};

/**
 * Get commercial contact info
 */
export const getCommercialContactInfo = (commercial: Commercial): string => {
  return `${commercial.email} | ${commercial.mobile}`;
};


