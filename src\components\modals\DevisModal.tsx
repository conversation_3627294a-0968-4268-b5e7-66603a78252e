import React, { useState, useEffect } from "react";
import { X, Save, Loader2, Plus, Trash2 } from "lucide-react";
import { <PERSON>s, DevisLineItem, DevisStatus } from "../../models";
import { useApp } from "../../hooks/useApp";

interface DevisModalProps {
  isOpen: boolean;
  onClose: () => void;
  devis?: Devis | null;
  mode: "create" | "edit" | "view";
}

const DevisModal: React.FC<DevisModalProps> = ({
  isOpen,
  onClose,
  devis,
  mode,
}) => {
  const { addDevis, updateDevis, clients, products } = useApp();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    clientId: "",
    numero: "",
    dateCreation: new Date().toISOString().split('T')[0],
    dateExpiration: "",
    statut: DevisStatus.BROUILLON,
    items: [] as DevisLineItem[],
    tauxTva: 20,
    remisePourcentage: 0,
    remiseMontant: 0,
    notes: "",
    conditionsValidite: "",
  });

  const isReadOnly = mode === "view";

  useEffect(() => {
    if (devis && (mode === "edit" || mode === "view")) {
      setFormData({
        clientId: devis.clientId || "",
        numero: devis.numero || "",
        dateCreation: devis.dateCreation ? devis.dateCreation.split('T')[0] : new Date().toISOString().split('T')[0],
        dateExpiration: devis.dateExpiration ? devis.dateExpiration.split('T')[0] : "",
        statut: devis.statut || DevisStatus.BROUILLON,
        items: devis.items || [],
        tauxTva: devis.tauxTva || 20,
        remisePourcentage: devis.remisePourcentage || 0,
        remiseMontant: devis.remiseMontant || 0,
        notes: devis.notes || "",
        conditionsValidite: devis.conditionsValidite || "",
      });
    } else {
      // Generate devis number for new devis
      const numero = `DEV-${Date.now()}`;
      setFormData({
        clientId: "",
        numero,
        dateCreation: new Date().toISOString().split('T')[0],
        dateExpiration: "",
        statut: DevisStatus.BROUILLON,
        items: [],
        tauxTva: 20,
        remisePourcentage: 0,
        remiseMontant: 0,
        notes: "",
        conditionsValidite: "",
      });
    }
    setError(null);
  }, [devis, mode, isOpen]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "tauxTva" || name === "remisePourcentage" || name === "remiseMontant" 
        ? Number(value) 
        : value
    }));
  };

  const addItemLine = () => {
    setFormData((prev) => ({
      ...prev,
      items: [
        ...prev.items,
        {
          codeProduit: "",
          nomProduit: "",
          prixUnitaire: 0,
          produitId: "",
          quantite: 1,
          sousTotal: 0,
          unite: "pièce",
        },
      ],
    }));
  };

  const removeItemLine = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }));
  };

  const updateItemLine = (
    index: number,
    field: string,
    value: string | number
  ) => {
    setFormData((prev) => {
      const newItems = [...prev.items];
      if (field === "produitId") {
        const selectedProduct = products.find((p) => p.id === value);
        newItems[index] = {
          ...newItems[index],
          produitId: value as string,
          nomProduit: selectedProduct?.nom || "",
          codeProduit: selectedProduct?.code || "",
          prixUnitaire: selectedProduct?.prix || 0,
          sousTotal: (selectedProduct?.prix || 0) * newItems[index].quantite,
        };
      } else if (field === "quantite") {
        newItems[index] = {
          ...newItems[index],
          quantite: value as number,
          sousTotal: newItems[index].prixUnitaire * (value as number),
        };
      } else {
        newItems[index] = {
          ...newItems[index],
          [field]: value,
        };
      }
      return { ...prev, items: newItems };
    });
  };

  const validateForm = () => {
    if (!formData.clientId) {
      setError("Le client est requis");
      return false;
    }
    if (!formData.numero.trim()) {
      setError("Le numéro de devis est requis");
      return false;
    }
    if (!formData.dateExpiration) {
      setError("La date d'expiration est requise");
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setLoading(true);
    setError(null);

    try {
      const devisData = {
        clientId: formData.clientId,
        numero: formData.numero.trim(),
        dateCreation: formData.dateCreation,
        dateExpiration: formData.dateExpiration,
        statut: formData.statut,
        items: formData.items.filter(
          (item) => item.produitId && item.quantite > 0
        ),
        tauxTva: formData.tauxTva,
        remisePourcentage: formData.remisePourcentage,
        remiseMontant: formData.remiseMontant,
        notes: formData.notes.trim(),
        conditionsValidite: formData.conditionsValidite.trim(),
      };

      if (mode === "create") {
        await addDevis(devisData);
      } else if (mode === "edit" && devis?.id) {
        await updateDevis(devis.id, devisData);
      }
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Une erreur est survenue");
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {mode === "create"
              ? "Nouveau Devis"
              : mode === "edit"
              ? "Modifier le Devis"
              : "Détails du Devis"}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <label
                htmlFor="clientId"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Client *
              </label>
              <select
                id="clientId"
                name="clientId"
                value={formData.clientId}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                required
              >
                <option value="">Sélectionner un client</option>
                {clients.map((client) => (
                  <option key={client.id} value={client.id}>
                    {client.nomClient}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label
                htmlFor="numero"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Numéro de devis *
              </label>
              <input
                type="text"
                id="numero"
                name="numero"
                value={formData.numero}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                required
              />
            </div>

            <div>
              <label
                htmlFor="dateCreation"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Date de création
              </label>
              <input
                type="date"
                id="dateCreation"
                name="dateCreation"
                value={formData.dateCreation}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              />
            </div>

            <div>
              <label
                htmlFor="dateExpiration"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Date d'expiration *
              </label>
              <input
                type="date"
                id="dateExpiration"
                name="dateExpiration"
                value={formData.dateExpiration}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                required
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Annuler
            </button>
            {!isReadOnly && (
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {loading && <Loader2 className="h-4 w-4 animate-spin" />}
                <Save className="h-4 w-4" />
                <span>{mode === "create" ? "Créer" : "Modifier"}</span>
              </button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
};

export default DevisModal;
